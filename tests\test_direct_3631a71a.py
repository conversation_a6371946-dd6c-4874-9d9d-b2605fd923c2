import numpy as np
import json
from scipy.ndimage import label

def all_rectangles(mat):
    mat = np.asarray(mat)
    results = {}
    for val in np.unique(mat):
        rects = []
        for r1 in range(mat.shape[0]):
            for c1 in range(mat.shape[1]):
                for r2 in range(r1, mat.shape[0]):
                    for c2 in range(c1, mat.shape[1]):
                        # Vérification : si le rectangle extrait est bien plein de val
                        # ET qu'il fait plus de 4 cellules
                        rect_size = (r2 - r1 + 1) * (c2 - c1 + 1)
                        if np.all(mat[r1:r2+1, c1:c2+1] == val) and rect_size > 4:
                            rects.append((r1, c1, r2, c2))
        if rects:
            # Fusionner les rectangles qui se chevauchent
            merged_rects = merge_overlapping_rectangles(rects)
            # Appliquer le masque pour chaque rectangle
            masked_results = apply_color_mask(mat, merged_rects, val)
            results[int(val)] = masked_results
    return results

def rectangles_overlap(rect1, rect2):
    """Vérifie si deux rectangles se chevauchent"""
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    return not (r2_1 < r1_2 or r2_2 < r1_1 or c2_1 < c1_2 or c2_2 < c1_1)

def merge_two_rectangles(rect1, rect2):
    """Fusionne deux rectangles en un rectangle englobant"""
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    return (min(r1_1, r1_2), min(c1_1, c1_2), max(r2_1, r2_2), max(c2_1, c2_2))

def merge_overlapping_rectangles(rectangles):
    """Fusionne tous les rectangles qui se chevauchent"""
    if not rectangles:
        return []
    
    merged = []
    used = set()
    
    for i, rect1 in enumerate(rectangles):
        if i in used:
            continue
        
        # Commencer avec le rectangle actuel
        current_merged = rect1
        overlapping_indices = {i}
        
        # Continuer à fusionner tant qu'on trouve des chevauchements
        changed = True
        while changed:
            changed = False
            for j, rect2 in enumerate(rectangles):
                if j not in overlapping_indices and rectangles_overlap(current_merged, rect2):
                    current_merged = merge_two_rectangles(current_merged, rect2)
                    overlapping_indices.add(j)
                    changed = True
        
        merged.append(current_merged)
        used.update(overlapping_indices)
    
    return merged

def apply_color_mask(mat, rectangles, target_color):
    """Applique un masque true/false pour chaque rectangle"""
    results = []
    
    for r1, c1, r2, c2 in rectangles:
        # Créer le masque true/false pour ce rectangle
        height = r2 - r1 + 1
        width = c2 - c1 + 1
        mask = []
        
        for r in range(r1, r2 + 1):
            row_mask = []
            for c in range(c1, c2 + 1):
                row_mask.append(mat[r, c] == target_color)
            mask.append(row_mask)
        
        # Formater le résultat
        rect_info = f"[{r1},{c1} {r2},{c2}]"
        mask_str = " MASK(" + str(mask).replace("True", "T").replace("False", "F") + ")"
        results.append(rect_info + mask_str)
    
    return results

# Charger la tâche 3631a71a
with open("arcdata/training/3631a71a.json", 'r', encoding='utf-8') as f:
    task_data = json.load(f)

# Test sur l'exemple de test
test_input = np.array(task_data['test'][0]['input'])
print("Test input shape:", test_input.shape)

# Afficher l'utilisation de chaque couleur
unique_values, counts = np.unique(test_input, return_counts=True)
total_cells = test_input.size
print("\nUtilisation des couleurs:")
for val, count in zip(unique_values, counts):
    percentage = (count / total_cells) * 100
    print(f"Couleur {val}: {count} cellules ({percentage:.1f}%)")

print(f"\nTotal: {total_cells} cellules")

# Vérifier le rectangle [3,20 11,26] pour la couleur 9
print("\nVérification du rectangle [3,20 11,26] pour la couleur 9:")
rect_content = test_input[3:12, 20:27]
print("Contenu du rectangle:")
print(rect_content)
print("Valeurs uniques:", np.unique(rect_content))
print("Toutes les valeurs sont 9?", np.all(rect_content == 9))

zones = all_rectangles(test_input)

# Filtrer les couleurs qui n'ont que des regroupements <= 4 cellules
print("\nRectangles détectés (> 4 cellules):")
filtered_zones = {}
for val, rects in zones.items():
    if rects:  # Si la couleur a des rectangles > 4 cellules
        filtered_zones[val] = rects
        print(f"Valeur {val} : {rects}")

# Afficher les couleurs supprimées
print("\nCouleurs supprimées (pas de rectangles > 4 cellules):")
for val in unique_values:
    if int(val) not in filtered_zones:
        print(f"Couleur {val}: {counts[unique_values == val][0]} cellules - pas de rectangles > 4 cellules")