#!/usr/bin/env python3
"""
Script d'entraînement pour DINO-ARC.

Implémente l'entraînement contrastif avec augmentations (rotations, permutations couleurs)
pour la catégorisation automatique des puzzles ARC.
"""

import sys
import os
sys.path.append('.')

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import random

from models.dino_arc import DinoARC, ContrastiveLoss, apply_augmentation
from src.multi_example_dataset import MultiExampleDataset
from src.scenario_generalizer import <PERSON>enarioGeneralizer


@dataclass
class TrainingConfig:
    """Configuration d'entraînement pour DINO-ARC."""
    # Modèle
    embed_dim: int = 128
    num_layers: int = 6
    num_heads: int = 8
    num_categories: int = 20
    
    # Entraînement
    batch_size: int = 8
    learning_rate: float = 1e-4
    num_epochs: int = 50
    warmup_epochs: int = 5
    
    # Contrastif
    temperature: float = 0.07
    augmentation_prob: float = 0.8
    
    # Données
    max_puzzles: Optional[int] = None
    train_split: float = 0.8
    
    # Sauvegarde
    save_every: int = 10
    checkpoint_dir: str = "checkpoints"
    log_every: int = 100
    
    # Device
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class ARCCategorizationDataset(Dataset):
    """
    Dataset pour l'entraînement de catégorisation ARC.
    
    Génère des paires contrastives avec augmentations pour l'apprentissage
    de représentations robustes.
    """
    
    def __init__(self, 
                 base_dataset: MultiExampleDataset,
                 config: TrainingConfig,
                 mode: str = "train"):
        """
        Args:
            base_dataset: Dataset de base avec puzzles multi-exemples
            config: Configuration d'entraînement
            mode: "train" ou "val"
        """
        self.base_dataset = base_dataset
        self.config = config
        self.mode = mode
        
        # Créer les étiquettes de catégories
        self.generalizer = ScenarioGeneralizer()
        self.category_to_id = {cat: i for i, cat in enumerate(self.generalizer.get_available_categories())}
        self.id_to_category = {i: cat for cat, i in self.category_to_id.items()}
        
        # Générer des pseudo-étiquettes basées sur des heuristiques simples
        self._generate_pseudo_labels()
    
    def _generate_pseudo_labels(self):
        """Génère des pseudo-étiquettes pour l'entraînement supervisé."""
        self.pseudo_labels = []
        
        print(f"Génération de pseudo-étiquettes pour {len(self.base_dataset)} puzzles...")
        
        for i in range(len(self.base_dataset)):
            sample = self.base_dataset[i]
            train_pairs = sample['train_pairs']
            
            # Heuristiques simples pour déterminer la catégorie
            category = self._infer_category_from_examples(train_pairs)
            category_id = self.category_to_id.get(category, self.category_to_id['unknown'])
            
            self.pseudo_labels.append(category_id)
        
        # Statistiques des catégories
        category_counts = {}
        for label in self.pseudo_labels:
            category = self.id_to_category[label]
            category_counts[category] = category_counts.get(category, 0) + 1
        
        print("Distribution des catégories:")
        for category, count in sorted(category_counts.items()):
            print(f"  {category}: {count} puzzles")
    
    def _infer_category_from_examples(self, train_pairs: List[Tuple[torch.Tensor, torch.Tensor]]) -> str:
        """
        Infère la catégorie d'un puzzle basé sur des heuristiques simples.
        
        Args:
            train_pairs: Paires (input, output) du puzzle
        
        Returns:
            Nom de la catégorie inférée
        """
        if not train_pairs:
            return 'unknown'
        
        # Analyser le premier exemple
        input_grid, output_grid = train_pairs[0]
        
        # Supprimer les dimensions batch si présentes
        if len(input_grid.shape) == 3:
            input_grid = input_grid.squeeze(0)
        if len(output_grid.shape) == 3:
            output_grid = output_grid.squeeze(0)
        
        # Heuristiques basées sur les changements observés
        
        # 1. Changement de couleur simple
        input_colors = set(input_grid.flatten().tolist())
        output_colors = set(output_grid.flatten().tolist())
        
        if len(input_colors) == len(output_colors) and input_colors != output_colors:
            return 'color_mapping'
        
        # 2. Ajout de couleurs (remplissage)
        if len(output_colors) > len(input_colors):
            return 'shape_completion'
        
        # 3. Même forme, couleurs différentes
        if input_grid.shape == output_grid.shape:
            diff_mask = input_grid != output_grid
            diff_ratio = diff_mask.float().mean().item()
            
            if 0.1 < diff_ratio < 0.9:
                return 'color_change'
            elif diff_ratio < 0.1:
                return 'template_match'
            else:
                return 'grid_transformation'
        
        # 4. Changement de taille
        if input_grid.shape != output_grid.shape:
            return 'scaling'
        
        # 5. Par défaut
        return 'unknown'
    
    def __len__(self):
        return len(self.base_dataset)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Retourne un échantillon d'entraînement avec augmentations contrastives.
        
        Returns:
            Dict contenant:
            - anchor_pairs: Paires originales
            - positive_pairs: Paires augmentées (même puzzle)
            - negative_pairs: Paires d'un autre puzzle
            - category_label: Étiquette de catégorie
        """
        # Échantillon principal (anchor)
        anchor_sample = self.base_dataset[idx]
        anchor_pairs = anchor_sample['train_pairs']
        category_label = self.pseudo_labels[idx]
        
        # Créer une vue positive (augmentée du même puzzle)
        positive_pairs = self._apply_augmentations(anchor_pairs)
        
        # Créer une vue négative (autre puzzle)
        negative_idx = random.randint(0, len(self.base_dataset) - 1)
        while negative_idx == idx:
            negative_idx = random.randint(0, len(self.base_dataset) - 1)
        
        negative_sample = self.base_dataset[negative_idx]
        negative_pairs = negative_sample['train_pairs']
        
        return {
            'anchor_pairs': anchor_pairs,
            'positive_pairs': positive_pairs,
            'negative_pairs': negative_pairs,
            'category_label': category_label,
            'puzzle_id': anchor_sample['puzzle_id']
        }
    
    def _apply_augmentations(self, train_pairs: List[Tuple[torch.Tensor, torch.Tensor]]) -> List[Tuple[torch.Tensor, torch.Tensor]]:
        """Applique des augmentations aux paires d'entraînement."""
        if random.random() > self.config.augmentation_prob:
            return train_pairs  # Pas d'augmentation
        
        # Choisir un type d'augmentation
        aug_types = ['rotation', 'flip', 'color_permutation']
        aug_type = random.choice(aug_types)
        
        augmented_pairs = []
        
        for input_grid, output_grid in train_pairs:
            # Supprimer dimension batch si présente
            if len(input_grid.shape) == 3:
                input_grid = input_grid.squeeze(0)
            if len(output_grid.shape) == 3:
                output_grid = output_grid.squeeze(0)
            
            # Appliquer la même augmentation aux deux grilles
            aug_input = apply_augmentation(input_grid.unsqueeze(0), aug_type).squeeze(0)
            aug_output = apply_augmentation(output_grid.unsqueeze(0), aug_type).squeeze(0)
            
            augmented_pairs.append((aug_input, aug_output))
        
        return augmented_pairs


class DinoARCTrainer:
    """Entraîneur pour le modèle DINO-ARC."""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # Créer le modèle
        self.model = DinoARC(
            embed_dim=config.embed_dim,
            num_layers=config.num_layers,
            num_heads=config.num_heads,
            num_categories=config.num_categories
        ).to(self.device)
        
        # Optimiseur
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-4
        )
        
        # Scheduler avec warmup
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.num_epochs - config.warmup_epochs
        )
        
        # Pertes
        self.contrastive_loss = ContrastiveLoss(temperature=config.temperature)
        self.classification_loss = nn.CrossEntropyLoss()
        
        # Métriques
        self.train_history = []
        self.val_history = []
        
        # Créer le dossier de checkpoints
        os.makedirs(config.checkpoint_dir, exist_ok=True)
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """Entraîne le modèle pour une époque."""
        self.model.train()
        
        total_loss = 0.0
        contrastive_loss_sum = 0.0
        classification_loss_sum = 0.0
        correct_predictions = 0
        total_predictions = 0
        
        for batch_idx, batch in enumerate(train_loader):
            self.optimizer.zero_grad()
            
            # Extraire les données
            anchor_pairs = batch['anchor_pairs']
            positive_pairs = batch['positive_pairs']
            negative_pairs = batch['negative_pairs']
            category_labels = batch['category_label'].to(self.device)
            
            # Forward pass pour anchor
            anchor_result = self.model(anchor_pairs)
            anchor_features = anchor_result['pattern_features']
            anchor_logits = anchor_result['category_logits']
            
            # Forward pass pour positive
            positive_result = self.model(positive_pairs)
            positive_features = positive_result['pattern_features']
            
            # Forward pass pour negative
            negative_result = self.model(negative_pairs)
            negative_features = negative_result['pattern_features']
            
            # Perte contrastive (anchor vs positive vs negative)
            contrastive_loss_pos = self.contrastive_loss(anchor_features, positive_features)
            contrastive_loss_neg = -self.contrastive_loss(anchor_features, negative_features)  # Négatif
            contrastive_loss_total = contrastive_loss_pos + contrastive_loss_neg
            
            # Perte de classification
            classification_loss = self.classification_loss(anchor_logits, category_labels)
            
            # Perte totale
            total_loss_batch = contrastive_loss_total + classification_loss
            
            # Backward pass
            total_loss_batch.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Métriques
            total_loss += total_loss_batch.item()
            contrastive_loss_sum += contrastive_loss_total.item()
            classification_loss_sum += classification_loss.item()
            
            # Précision de classification
            _, predicted = torch.max(anchor_logits, 1)
            correct_predictions += (predicted == category_labels).sum().item()
            total_predictions += category_labels.size(0)
            
            # Log
            if batch_idx % self.config.log_every == 0:
                print(f"  Batch {batch_idx}/{len(train_loader)}: "
                      f"Loss={total_loss_batch.item():.4f}, "
                      f"Contrastive={contrastive_loss_total.item():.4f}, "
                      f"Classification={classification_loss.item():.4f}")
        
        # Métriques moyennes
        avg_loss = total_loss / len(train_loader)
        avg_contrastive = contrastive_loss_sum / len(train_loader)
        avg_classification = classification_loss_sum / len(train_loader)
        accuracy = correct_predictions / total_predictions
        
        return {
            'loss': avg_loss,
            'contrastive_loss': avg_contrastive,
            'classification_loss': avg_classification,
            'accuracy': accuracy
        }
    
    def validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """Valide le modèle pour une époque."""
        self.model.eval()
        
        total_loss = 0.0
        correct_predictions = 0
        total_predictions = 0
        
        with torch.no_grad():
            for batch in val_loader:
                anchor_pairs = batch['anchor_pairs']
                category_labels = batch['category_label'].to(self.device)
                
                # Forward pass
                result = self.model(anchor_pairs)
                logits = result['category_logits']
                
                # Perte de classification
                loss = self.classification_loss(logits, category_labels)
                total_loss += loss.item()
                
                # Précision
                _, predicted = torch.max(logits, 1)
                correct_predictions += (predicted == category_labels).sum().item()
                total_predictions += category_labels.size(0)
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct_predictions / total_predictions
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy
        }
    
    def train(self, train_dataset: ARCCategorizationDataset, val_dataset: ARCCategorizationDataset):
        """Entraîne le modèle complet."""
        print(f"🚀 Début de l'entraînement DINO-ARC")
        print(f"Device: {self.device}")
        print(f"Modèle: {sum(p.numel() for p in self.model.parameters())} paramètres")
        print(f"Dataset train: {len(train_dataset)} puzzles")
        print(f"Dataset val: {len(val_dataset)} puzzles")
        
        # DataLoaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True if self.device.type == 'cuda' else False
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True if self.device.type == 'cuda' else False
        )
        
        best_val_accuracy = 0.0
        
        for epoch in range(self.config.num_epochs):
            start_time = time.time()
            
            print(f"\n📊 Époque {epoch+1}/{self.config.num_epochs}")
            
            # Entraînement
            train_metrics = self.train_epoch(train_loader)
            
            # Validation
            val_metrics = self.validate_epoch(val_loader)
            
            # Scheduler (après warmup)
            if epoch >= self.config.warmup_epochs:
                self.scheduler.step()
            
            # Temps
            epoch_time = time.time() - start_time
            
            # Log
            print(f"Train - Loss: {train_metrics['loss']:.4f}, "
                  f"Contrastive: {train_metrics['contrastive_loss']:.4f}, "
                  f"Classification: {train_metrics['classification_loss']:.4f}, "
                  f"Accuracy: {train_metrics['accuracy']:.3f}")
            print(f"Val   - Loss: {val_metrics['loss']:.4f}, "
                  f"Accuracy: {val_metrics['accuracy']:.3f}")
            print(f"Temps: {epoch_time:.1f}s, LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # Sauvegarder l'historique
            self.train_history.append(train_metrics)
            self.val_history.append(val_metrics)
            
            # Sauvegarder le meilleur modèle
            if val_metrics['accuracy'] > best_val_accuracy:
                best_val_accuracy = val_metrics['accuracy']
                self.save_checkpoint(epoch, 'best_model.pth', val_metrics)
                print(f"💾 Nouveau meilleur modèle sauvegardé (accuracy: {best_val_accuracy:.3f})")
            
            # Sauvegarder périodiquement
            if (epoch + 1) % self.config.save_every == 0:
                self.save_checkpoint(epoch, f'checkpoint_epoch_{epoch+1}.pth', val_metrics)
        
        print(f"\n🎉 Entraînement terminé!")
        print(f"Meilleure accuracy de validation: {best_val_accuracy:.3f}")
        
        # Sauvegarder l'historique
        self.save_training_history()
    
    def save_checkpoint(self, epoch: int, filename: str, metrics: Dict[str, float]):
        """Sauvegarde un checkpoint."""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'config': self.config
        }
        
        filepath = os.path.join(self.config.checkpoint_dir, filename)
        torch.save(checkpoint, filepath)
    
    def save_training_history(self):
        """Sauvegarde l'historique d'entraînement."""
        history = {
            'train_history': self.train_history,
            'val_history': self.val_history,
            'config': self.config.__dict__
        }
        
        filepath = os.path.join(self.config.checkpoint_dir, 'training_history.json')
        with open(filepath, 'w') as f:
            json.dump(history, f, indent=2, default=str)
        
        print(f"📈 Historique sauvegardé: {filepath}")
    
    def load_checkpoint(self, filepath: str):
        """Charge un checkpoint."""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        return checkpoint['epoch'], checkpoint['metrics']


def create_datasets(config: TrainingConfig) -> Tuple[ARCCategorizationDataset, ARCCategorizationDataset]:
    """Crée les datasets d'entraînement et de validation."""
    print("📂 Chargement des données...")
    
    # Dataset de base
    base_dataset = MultiExampleDataset('arcdata/training')
    
    if config.max_puzzles:
        # Limiter le nombre de puzzles pour les tests
        indices = list(range(min(config.max_puzzles, len(base_dataset))))
        base_dataset.data = [base_dataset.data[i] for i in indices]
    
    # Split train/val
    total_size = len(base_dataset)
    train_size = int(total_size * config.train_split)
    
    # Créer les indices
    indices = list(range(total_size))
    random.shuffle(indices)
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:]
    
    # Créer les sous-datasets
    train_base_data = [base_dataset.data[i] for i in train_indices]
    val_base_data = [base_dataset.data[i] for i in val_indices]
    
    # Créer des copies du dataset avec les bonnes données
    train_base_dataset = MultiExampleDataset.__new__(MultiExampleDataset)
    train_base_dataset.__dict__.update(base_dataset.__dict__)
    train_base_dataset.data = train_base_data
    
    val_base_dataset = MultiExampleDataset.__new__(MultiExampleDataset)
    val_base_dataset.__dict__.update(base_dataset.__dict__)
    val_base_dataset.data = val_base_data
    
    # Créer les datasets de catégorisation
    train_dataset = ARCCategorizationDataset(train_base_dataset, config, mode="train")
    val_dataset = ARCCategorizationDataset(val_base_dataset, config, mode="val")
    
    print(f"Dataset train: {len(train_dataset)} puzzles")
    print(f"Dataset val: {len(val_dataset)} puzzles")
    
    return train_dataset, val_dataset


def main():
    """Fonction principale d'entraînement."""
    # Configuration
    config = TrainingConfig(
        embed_dim=64,  # Réduit pour les tests
        num_layers=4,
        num_heads=4,
        batch_size=4,
        num_epochs=20,
        max_puzzles=100,  # Limiter pour les tests
        learning_rate=1e-3,
        log_every=10
    )
    
    print("🔧 Configuration d'entraînement:")
    for key, value in config.__dict__.items():
        print(f"  {key}: {value}")
    
    # Créer les datasets
    train_dataset, val_dataset = create_datasets(config)
    
    # Créer l'entraîneur
    trainer = DinoARCTrainer(config)
    
    # Entraîner
    trainer.train(train_dataset, val_dataset)


if __name__ == "__main__":
    main()