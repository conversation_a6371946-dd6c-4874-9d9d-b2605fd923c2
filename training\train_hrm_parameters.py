#!/usr/bin/env python3
"""
Script d'entraînement pour HRM Parameter Resolution.

Entraîne le résolveur de paramètres HRM à instancier correctement
les templates généralisés en analysant les exemples train.
"""

import sys
import os
sys.path.append('.')

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import random

from models.hrm_parameter_resolver import HRMParameterResolver, ParameterAnalyzer
from src.scenario_generalizer import ScenarioGeneralizer, GeneralizedScenario, ParameterConstraint, ParameterType
from src.multi_example_dataset import MultiExampleDataset


@dataclass
class HRMTrainingConfig:
    """Configuration d'entraînement pour HRM Parameter Resolution."""
    # Modèle
    hidden_dim: int = 128
    num_layers: int = 3
    
    # Entraînement
    batch_size: int = 16
    learning_rate: float = 1e-3
    num_epochs: int = 30
    warmup_epochs: int = 3
    
    # Données
    max_puzzles: Optional[int] = None
    train_split: float = 0.8
    
    # Sauvegarde
    save_every: int = 5
    checkpoint_dir: str = "hrm_checkpoints"
    log_every: int = 50
    
    # Device
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class ParameterResolutionDataset(Dataset):
    """
    Dataset pour l'entraînement de résolution de paramètres HRM.
    
    Génère des exemples (scénario, puzzle) -> paramètres résolus
    pour apprendre la correspondance template → instanciation.
    """
    
    def __init__(self, 
                 base_dataset: MultiExampleDataset,
                 config: HRMTrainingConfig,
                 mode: str = "train"):
        """
        Args:
            base_dataset: Dataset de base avec puzzles multi-exemples
            config: Configuration d'entraînement
            mode: "train" ou "val"
        """
        self.base_dataset = base_dataset
        self.config = config
        self.mode = mode
        
        # Composants pour générer les données d'entraînement
        self.generalizer = ScenarioGeneralizer()
        self.resolver = HRMParameterResolver()
        self.analyzer = ParameterAnalyzer()
        
        # Générer les exemples d'entraînement
        self._generate_training_examples()
    
    def _generate_training_examples(self):
        """Génère les exemples d'entraînement scénario → paramètres."""
        self.training_examples = []
        
        print(f"Génération d'exemples d'entraînement HRM pour {len(self.base_dataset)} puzzles...")
        
        categories = self.generalizer.get_available_categories()
        
        for i in range(len(self.base_dataset)):
            sample = self.base_dataset[i]
            train_pairs = sample['train_pairs']
            test_input = sample['test_input']
            
            # Générer plusieurs scénarios pour ce puzzle
            for category in categories[:5]:  # Limiter à 5 catégories pour les tests
                scenario = self.generalizer.generate_scenario(category)
                
                if len(scenario.constraints) == 0:
                    continue  # Ignorer les scénarios sans contraintes
                
                try:
                    # Résoudre les paramètres avec le resolver actuel
                    resolved_params = self.resolver.resolve_parameters(
                        scenario, test_input, train_pairs
                    )
                    
                    # Créer un exemple d'entraînement
                    example = {
                        'scenario': scenario,
                        'train_pairs': train_pairs,
                        'test_input': test_input,
                        'resolved_params': resolved_params,
                        'puzzle_id': sample['puzzle_id'],
                        'category': category
                    }
                    
                    self.training_examples.append(example)
                    
                except Exception as e:
                    # Ignorer les exemples qui échouent
                    continue
        
        print(f"Généré {len(self.training_examples)} exemples d'entraînement")
        
        # Statistiques par catégorie
        category_counts = {}
        for example in self.training_examples:
            cat = example['category']
            category_counts[cat] = category_counts.get(cat, 0) + 1
        
        print("Distribution des exemples par catégorie:")
        for cat, count in sorted(category_counts.items()):
            print(f"  {cat}: {count} exemples")
    
    def __len__(self):
        return len(self.training_examples)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Retourne un exemple d'entraînement.
        
        Returns:
            Dict contenant les features d'entrée et les targets
        """
        example = self.training_examples[idx]
        
        # Extraire les features d'entrée
        scenario = example['scenario']
        train_pairs = example['train_pairs']
        test_input = example['test_input']
        
        # Features du scénario
        scenario_features = self._encode_scenario(scenario)
        
        # Features des grilles
        grid_features = self._encode_grids(train_pairs, test_input)
        
        # Targets (paramètres résolus)
        param_targets = self._encode_parameters(example['resolved_params'])
        
        return {
            'scenario_features': scenario_features,
            'grid_features': grid_features,
            'param_targets': param_targets,
            'category': example['category'],
            'puzzle_id': example['puzzle_id']
        }
    
    def _encode_scenario(self, scenario: GeneralizedScenario) -> torch.Tensor:
        """Encode un scénario en features numériques."""
        # Encoding simple basé sur la catégorie et le nombre de contraintes
        categories = self.generalizer.get_available_categories()
        category_id = categories.index(scenario.category) if scenario.category in categories else 0
        
        # Features: [category_id, num_constraints, confidence]
        features = torch.tensor([
            category_id,
            len(scenario.constraints),
            scenario.confidence
        ], dtype=torch.float32)
        
        return features
    
    def _encode_grids(self, train_pairs: List[Tuple[torch.Tensor, torch.Tensor]], 
                     test_input: torch.Tensor) -> torch.Tensor:
        """Encode les grilles en features numériques."""
        # Features simples basées sur les statistiques des grilles
        features = []
        
        # Features du test_input
        if len(test_input.shape) == 3:
            test_input = test_input.squeeze(0)
        
        test_colors = torch.unique(test_input).tolist()
        test_size = test_input.shape[0] * test_input.shape[1]
        
        features.extend([
            len(test_colors),  # Nombre de couleurs uniques
            test_size,         # Taille de la grille
            test_input.float().mean().item()  # Couleur moyenne
        ])
        
        # Features des train_pairs (moyennées)
        if train_pairs:
            input_colors_avg = 0
            output_colors_avg = 0
            size_avg = 0
            
            for input_grid, output_grid in train_pairs:
                if len(input_grid.shape) == 3:
                    input_grid = input_grid.squeeze(0)
                if len(output_grid.shape) == 3:
                    output_grid = output_grid.squeeze(0)
                
                input_colors_avg += len(torch.unique(input_grid))
                output_colors_avg += len(torch.unique(output_grid))
                size_avg += input_grid.shape[0] * input_grid.shape[1]
            
            n_pairs = len(train_pairs)
            features.extend([
                input_colors_avg / n_pairs,
                output_colors_avg / n_pairs,
                size_avg / n_pairs
            ])
        else:
            features.extend([0, 0, 0])
        
        return torch.tensor(features, dtype=torch.float32)
    
    def _encode_parameters(self, resolved_params: Dict) -> torch.Tensor:
        """Encode les paramètres résolus en targets numériques."""
        # Encoding simple : convertir les valeurs en nombres
        encoded_values = []
        
        for param_name, param in resolved_params.items():
            # Essayer de convertir la valeur en nombre
            try:
                if param.resolved_value.isdigit():
                    encoded_values.append(float(param.resolved_value))
                elif 'x' in param.resolved_value:  # Format taille
                    w, h = param.resolved_value.split('x')
                    encoded_values.extend([float(w), float(h)])
                elif '[' in param.resolved_value:  # Format position
                    # Extraire les coordonnées
                    coords = param.resolved_value.replace('[', '').replace(']', '').split(',')
                    if len(coords) >= 2:
                        encoded_values.extend([float(coords[0]), float(coords[1])])
                    else:
                        encoded_values.extend([0.0, 0.0])
                else:
                    # Hash de la string pour les valeurs non-numériques
                    encoded_values.append(float(hash(param.resolved_value) % 100))
            except:
                encoded_values.append(0.0)
        
        # Padding/truncation à une taille fixe
        target_size = 10  # Taille fixe pour les targets
        if len(encoded_values) > target_size:
            encoded_values = encoded_values[:target_size]
        else:
            encoded_values.extend([0.0] * (target_size - len(encoded_values)))
        
        return torch.tensor(encoded_values, dtype=torch.float32)


class HRMParameterNet(nn.Module):
    """
    Réseau de neurones pour la résolution de paramètres HRM.
    
    Prend en entrée les features du scénario et des grilles,
    et prédit les paramètres résolus.
    """
    
    def __init__(self, config: HRMTrainingConfig):
        super().__init__()
        
        # Dimensions des features
        scenario_dim = 3  # [category_id, num_constraints, confidence]
        grid_dim = 6      # Features des grilles
        
        input_dim = scenario_dim + grid_dim
        hidden_dim = config.hidden_dim
        output_dim = 10   # Taille fixe des paramètres encodés
        
        # Architecture du réseau
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, output_dim)
        )
        
        # Initialisation des poids
        self._init_weights()
    
    def _init_weights(self):
        """Initialise les poids du réseau."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, scenario_features: torch.Tensor, grid_features: torch.Tensor) -> torch.Tensor:
        """
        Forward pass du réseau.
        
        Args:
            scenario_features: Features du scénario [batch, 3]
            grid_features: Features des grilles [batch, 6]
        
        Returns:
            Paramètres prédits [batch, 10]
        """
        # Concaténer les features
        combined_features = torch.cat([scenario_features, grid_features], dim=1)
        
        # Passage dans le réseau
        output = self.network(combined_features)
        
        return output


class HRMParameterTrainer:
    """Entraîneur pour le réseau de résolution de paramètres HRM."""
    
    def __init__(self, config: HRMTrainingConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # Créer le modèle
        self.model = HRMParameterNet(config).to(self.device)
        
        # Optimiseur
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=config.learning_rate,
            weight_decay=1e-4
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.num_epochs - config.warmup_epochs
        )
        
        # Perte
        self.criterion = nn.MSELoss()
        
        # Métriques
        self.train_history = []
        self.val_history = []
        
        # Créer le dossier de checkpoints
        os.makedirs(config.checkpoint_dir, exist_ok=True)
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """Entraîne le modèle pour une époque."""
        self.model.train()
        
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, batch in enumerate(train_loader):
            self.optimizer.zero_grad()
            
            # Extraire les données
            scenario_features = batch['scenario_features'].to(self.device)
            grid_features = batch['grid_features'].to(self.device)
            param_targets = batch['param_targets'].to(self.device)
            
            # Forward pass
            predictions = self.model(scenario_features, grid_features)
            
            # Calcul de la perte
            loss = self.criterion(predictions, param_targets)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Métriques
            total_loss += loss.item()
            num_batches += 1
            
            # Log
            if batch_idx % self.config.log_every == 0:
                print(f"  Batch {batch_idx}/{len(train_loader)}: Loss={loss.item():.4f}")
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        return {'loss': avg_loss}
    
    def validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """Valide le modèle pour une époque."""
        self.model.eval()
        
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                scenario_features = batch['scenario_features'].to(self.device)
                grid_features = batch['grid_features'].to(self.device)
                param_targets = batch['param_targets'].to(self.device)
                
                # Forward pass
                predictions = self.model(scenario_features, grid_features)
                
                # Calcul de la perte
                loss = self.criterion(predictions, param_targets)
                
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        return {'loss': avg_loss}
    
    def train(self, train_dataset: ParameterResolutionDataset, val_dataset: ParameterResolutionDataset):
        """Entraîne le modèle complet."""
        print(f"🚀 Début de l'entraînement HRM Parameter Resolution")
        print(f"Device: {self.device}")
        print(f"Modèle: {sum(p.numel() for p in self.model.parameters())} paramètres")
        print(f"Dataset train: {len(train_dataset)} exemples")
        print(f"Dataset val: {len(val_dataset)} exemples")
        
        # DataLoaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.batch_size,
            shuffle=True,
            num_workers=2,
            pin_memory=True if self.device.type == 'cuda' else False
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=2,
            pin_memory=True if self.device.type == 'cuda' else False
        )
        
        best_val_loss = float('inf')
        
        for epoch in range(self.config.num_epochs):
            start_time = time.time()
            
            print(f"\n📊 Époque {epoch+1}/{self.config.num_epochs}")
            
            # Entraînement
            train_metrics = self.train_epoch(train_loader)
            
            # Validation
            val_metrics = self.validate_epoch(val_loader)
            
            # Scheduler (après warmup)
            if epoch >= self.config.warmup_epochs:
                self.scheduler.step()
            
            # Temps
            epoch_time = time.time() - start_time
            
            # Log
            print(f"Train Loss: {train_metrics['loss']:.4f}")
            print(f"Val Loss: {val_metrics['loss']:.4f}")
            print(f"Temps: {epoch_time:.1f}s, LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # Sauvegarder l'historique
            self.train_history.append(train_metrics)
            self.val_history.append(val_metrics)
            
            # Sauvegarder le meilleur modèle
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                self.save_checkpoint(epoch, 'best_hrm_model.pth', val_metrics)
                print(f"💾 Nouveau meilleur modèle sauvegardé (loss: {best_val_loss:.4f})")
            
            # Sauvegarder périodiquement
            if (epoch + 1) % self.config.save_every == 0:
                self.save_checkpoint(epoch, f'hrm_checkpoint_epoch_{epoch+1}.pth', val_metrics)
        
        print(f"\n🎉 Entraînement HRM terminé!")
        print(f"Meilleure loss de validation: {best_val_loss:.4f}")
        
        # Sauvegarder l'historique
        self.save_training_history()
    
    def save_checkpoint(self, epoch: int, filename: str, metrics: Dict[str, float]):
        """Sauvegarde un checkpoint."""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'metrics': metrics,
            'config': self.config
        }
        
        filepath = os.path.join(self.config.checkpoint_dir, filename)
        torch.save(checkpoint, filepath)
    
    def save_training_history(self):
        """Sauvegarde l'historique d'entraînement."""
        history = {
            'train_history': self.train_history,
            'val_history': self.val_history,
            'config': self.config.__dict__
        }
        
        filepath = os.path.join(self.config.checkpoint_dir, 'hrm_training_history.json')
        with open(filepath, 'w') as f:
            json.dump(history, f, indent=2, default=str)
        
        print(f"📈 Historique HRM sauvegardé: {filepath}")
    
    def load_checkpoint(self, filepath: str):
        """Charge un checkpoint."""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        return checkpoint['epoch'], checkpoint['metrics']


def create_hrm_datasets(config: HRMTrainingConfig) -> Tuple[ParameterResolutionDataset, ParameterResolutionDataset]:
    """Crée les datasets d'entraînement et de validation pour HRM."""
    print("📂 Chargement des données pour HRM...")
    
    # Dataset de base
    base_dataset = MultiExampleDataset('arcdata/training')
    
    if config.max_puzzles:
        # Limiter le nombre de puzzles pour les tests
        indices = list(range(min(config.max_puzzles, len(base_dataset))))
        base_dataset.data = [base_dataset.data[i] for i in indices]
    
    # Split train/val
    total_size = len(base_dataset)
    train_size = int(total_size * config.train_split)
    
    # Créer les indices
    indices = list(range(total_size))
    random.shuffle(indices)
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:]
    
    # Créer les sous-datasets
    train_base_data = [base_dataset.data[i] for i in train_indices]
    val_base_data = [base_dataset.data[i] for i in val_indices]
    
    # Créer des copies du dataset avec les bonnes données
    train_base_dataset = MultiExampleDataset.__new__(MultiExampleDataset)
    train_base_dataset.__dict__.update(base_dataset.__dict__)
    train_base_dataset.data = train_base_data
    
    val_base_dataset = MultiExampleDataset.__new__(MultiExampleDataset)
    val_base_dataset.__dict__.update(base_dataset.__dict__)
    val_base_dataset.data = val_base_data
    
    # Créer les datasets de résolution de paramètres
    train_dataset = ParameterResolutionDataset(train_base_dataset, config, mode="train")
    val_dataset = ParameterResolutionDataset(val_base_dataset, config, mode="val")
    
    print(f"Dataset HRM train: {len(train_dataset)} exemples")
    print(f"Dataset HRM val: {len(val_dataset)} exemples")
    
    return train_dataset, val_dataset


def main():
    """Fonction principale d'entraînement HRM."""
    # Configuration
    config = HRMTrainingConfig(
        hidden_dim=64,  # Réduit pour les tests
        num_layers=2,
        batch_size=8,
        num_epochs=15,
        max_puzzles=50,  # Limiter pour les tests
        learning_rate=1e-3,
        log_every=5,
        device="cpu"  # Forcer CPU pour éviter les problèmes de device
    )
    
    print("🔧 Configuration d'entraînement HRM:")
    for key, value in config.__dict__.items():
        print(f"  {key}: {value}")
    
    # Créer les datasets
    train_dataset, val_dataset = create_hrm_datasets(config)
    
    # Créer l'entraîneur
    trainer = HRMParameterTrainer(config)
    
    # Entraîner
    trainer.train(train_dataset, val_dataset)


if __name__ == "__main__":
    main()