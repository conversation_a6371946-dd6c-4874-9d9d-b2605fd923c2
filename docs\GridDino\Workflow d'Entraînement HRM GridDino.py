# Initialisation
model = HRM_GridDINO(grid_size=30, vocab_size=10)
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

# Boucle d'entraînement
for batch in dataloader:
    input_grid, action_seq, target_grid = batch
    
    # Forward pass
    transformed_grid, recon_pred, action_pred = model(input_grid, action_seq)
    
    # Calcul des pertes
    transform_loss = F.mse_loss(transformed_grid, target_grid)
    recon_loss = F.cross_entropy(recon_pred, input_grid)
    action_loss = F.cross_entropy(action_pred, action_seq)
    
    # Perte totale pondérée
    loss = 0.7 * transform_loss + 0.2 * recon_loss + 0.1 * action_loss
    
    # Rétropropagation
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()