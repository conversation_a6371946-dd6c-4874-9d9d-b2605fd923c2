#!/usr/bin/env python3
"""
Script de test pour le modèle DINO-ARC.

Valide que le modèle peut analyser des paires multi-exemples
et effectuer la catégorisation automatique.
"""

import sys
import os
sys.path.append('.')

import torch
import numpy as np
from models.dino_arc import DinoARC, apply_augmentation, ContrastiveLoss
from src.multi_example_dataset import MultiExampleDataset


def create_synthetic_puzzle():
    """Crée un puzzle synthétique pour les tests."""
    # Puzzle simple : copier la couleur dominante
    train_pairs = []
    
    # Exemple 1: grille 3x3 avec couleur 1 dominante -> output tout en 1
    input1 = torch.tensor([[1, 1, 0], [1, 1, 1], [0, 1, 1]], dtype=torch.long)
    output1 = torch.ones(3, 3, dtype=torch.long)
    
    # Exemple 2: grille 3x3 avec couleur 2 dominante -> output tout en 2  
    input2 = torch.tensor([[2, 0, 2], [2, 2, 2], [2, 2, 0]], dtype=torch.long)
    output2 = torch.full((3, 3), 2, dtype=torch.long)
    
    # Padding à 30x30
    def pad_grid(grid):
        padded = torch.zeros(30, 30, dtype=torch.long)
        padded[:3, :3] = grid
        return padded
    
    train_pairs = [
        (pad_grid(input1).unsqueeze(0), pad_grid(output1).unsqueeze(0)),
        (pad_grid(input2).unsqueeze(0), pad_grid(output2).unsqueeze(0))
    ]
    
    return train_pairs


def test_model_creation():
    """Test de création du modèle DINO-ARC."""
    print("=== Test création modèle DINO-ARC ===")
    
    model = DinoARC(
        grid_size=30,
        vocab_size=10,
        embed_dim=128,
        num_layers=4,  # Réduit pour les tests
        num_heads=8,
        num_categories=20
    )
    
    print(f"Modèle créé avec {sum(p.numel() for p in model.parameters())} paramètres")
    print(f"Catégories disponibles: {len(model.categorizer.categories)}")
    
    return model


def test_single_grid_encoding():
    """Test d'encodage d'une grille individuelle."""
    print("\n=== Test encodage grille individuelle ===")
    
    model = DinoARC(embed_dim=64, num_layers=2)  # Modèle réduit pour test
    
    # Grille de test
    grid = torch.randint(0, 10, (1, 30, 30))
    
    # Encodage
    features, pattern_maps = model.encode_grid(grid)
    
    print(f"Features shape: {features.shape}")
    print(f"Pattern maps: {list(pattern_maps.keys())}")
    
    for ptype, pmap in pattern_maps.items():
        print(f"  {ptype}: {pmap.shape}")
    
    assert features.shape == (1, 30, 30, 64), f"Features shape incorrecte: {features.shape}"
    assert all(pmap.shape == (1, 30, 30) for pmap in pattern_maps.values()), "Pattern maps shape incorrecte"
    
    print("✓ Encodage grille individuelle OK")


def test_multi_example_analysis():
    """Test d'analyse multi-exemples."""
    print("\n=== Test analyse multi-exemples ===")
    
    model = DinoARC(embed_dim=64, num_layers=2)
    
    # Puzzle synthétique
    train_pairs = create_synthetic_puzzle()
    
    print(f"Puzzle avec {len(train_pairs)} exemples train")
    
    # Analyse
    with torch.no_grad():
        result = model(train_pairs)
    
    print(f"Catégorie prédite: {result['category']}")
    print(f"Confiance: {result['confidence']:.3f}")
    print(f"Pattern features shape: {result['pattern_features'].shape}")
    print(f"Pattern maps: {list(result['pattern_maps'].keys())}")
    
    # Vérifications
    assert isinstance(result['category'], str), "Catégorie doit être une string"
    assert 0 <= result['confidence'] <= 1, f"Confiance invalide: {result['confidence']}"
    assert result['pattern_features'].shape == (1, 64), f"Pattern features shape incorrecte"
    
    print("✓ Analyse multi-exemples OK")


def test_augmentations():
    """Test des augmentations pour l'entraînement contrastif."""
    print("\n=== Test augmentations ===")
    
    # Grille de test
    grid = torch.tensor([[[1, 2, 0], [3, 1, 2], [0, 3, 1]]], dtype=torch.long)
    
    # Test rotation
    rotated = apply_augmentation(grid, 'rotation')
    print(f"Original shape: {grid.shape}, Rotated shape: {rotated.shape}")
    assert rotated.shape == grid.shape, "Shape doit être préservée après rotation"
    
    # Test flip
    flipped = apply_augmentation(grid, 'flip')
    print(f"Flipped shape: {flipped.shape}")
    assert flipped.shape == grid.shape, "Shape doit être préservée après flip"
    
    # Test permutation couleurs
    color_perm = apply_augmentation(grid, 'color_permutation')
    print(f"Color permuted shape: {color_perm.shape}")
    assert color_perm.shape == grid.shape, "Shape doit être préservée après permutation"
    
    print("✓ Augmentations OK")


def test_contrastive_loss():
    """Test de la perte contrastive."""
    print("\n=== Test perte contrastive ===")
    
    # Features simulées
    features1 = torch.randn(4, 128)
    features2 = torch.randn(4, 128)
    
    # Perte contrastive
    contrastive_loss = ContrastiveLoss(temperature=0.07)
    loss = contrastive_loss(features1, features2)
    
    print(f"Perte contrastive: {loss.item():.4f}")
    assert loss.item() > 0, "Perte doit être positive"
    
    print("✓ Perte contrastive OK")


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    model = DinoARC(embed_dim=64, num_layers=2)
    train_pairs = create_synthetic_puzzle()
    
    with torch.no_grad():
        result = model(train_pairs)
    
    # Requirement 2.1: Détecter patterns communs
    assert 'pattern_features' in result, "Doit retourner des pattern features"
    assert result['pattern_features'].numel() > 0, "Pattern features ne peuvent pas être vides"
    print("✓ Requirement 2.1: Détection patterns communs OK")
    
    # Requirement 2.2: Catégorisation automatique
    assert 'category' in result, "Doit retourner une catégorie"
    assert result['category'] in model.categorizer.categories, "Catégorie doit être valide"
    print(f"✓ Requirement 2.2: Catégorisation automatique OK ({result['category']})")
    
    # Requirement 2.3: Génération scénario généralisé (préparation)
    assert 'confidence' in result, "Doit retourner une confiance"
    assert 0 <= result['confidence'] <= 1, "Confiance doit être entre 0 et 1"
    print(f"✓ Requirement 2.3: Préparation scénario généralisé OK (confiance: {result['confidence']:.3f})")


def test_with_real_data():
    """Test avec des données réelles si disponibles."""
    print("\n=== Test avec données réelles ===")
    
    try:
        # Essayer de charger des données réelles
        dataset = MultiExampleDataset('arcdata/training')
        
        if len(dataset) == 0:
            print("Pas de données réelles disponibles, test ignoré")
            return
        
        # Prendre le premier puzzle
        sample = dataset[0]
        train_pairs = sample['train_pairs']
        
        print(f"Test avec puzzle réel: {sample['puzzle_id']}")
        print(f"Nombre d'exemples: {len(train_pairs)}")
        
        # Convertir en format attendu par le modèle
        model_train_pairs = [(inp.unsqueeze(0), out.unsqueeze(0)) for inp, out in train_pairs]
        
        model = DinoARC(embed_dim=64, num_layers=2)
        
        with torch.no_grad():
            result = model(model_train_pairs)
        
        print(f"Catégorie prédite: {result['category']}")
        print(f"Confiance: {result['confidence']:.3f}")
        
        print("✓ Test avec données réelles OK")
        
    except Exception as e:
        print(f"Test avec données réelles échoué: {e}")
        print("Continuons avec les tests synthétiques")


def main():
    """Fonction principale de test."""
    print("Test du modèle DINO-ARC pour analyse multi-exemples")
    print("=" * 55)
    
    success = True
    
    try:
        model = test_model_creation()
        test_single_grid_encoding()
        test_multi_example_analysis()
        test_augmentations()
        test_contrastive_loss()
        test_requirements_compliance()
        test_with_real_data()
        
        print("\n🎉 Tous les tests sont passés!")
        print("Le modèle DINO-ARC est prêt pour l'analyse multi-exemples")
        
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)