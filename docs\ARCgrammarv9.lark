﻿// ===================================================================
// == Grammaire pour le Langage de Scénario de Résolution de Puzzle ==
// == Version 9 - Mise à jour selon FONCTIONNEMENT_COMMANDES_SCENARIOS.md
// ===================================================================

// --- RÈGLE DE DÉPART : STRUCTURE GLOBALE DU SCRIPT ---
?start: initial_command (";" middle_command)* "END"?

// --- COMMANDES STRUCTURELLES (Début de script) ---
initial_command: transfer_command | init_command
transfer_command: "TRANSFERT" "{" (middle_command ";")* "}"
init_command: "INIT" GRID_SIZE

// --- COMMANDES INTERMÉDIAIRES (Corps du script) ---
middle_command: command_with_location | transformation_command | grouped_commands

// Commandes avec coordonnées
command_with_location: (
    edit_command
    | fill_command
    | clear_command
    | replace_command
    | surround_command
    | clipboard_command
    | extract_command
    | insert_command
    | delete_command
) location

// Commandes groupées
grouped_commands: (
    "EDITS" "{" (edit_command location ";")* "}"
    | "FILLS" "{" (fill_command location ";")* "}"
    | "CLEARS" "{" (clear_command location ";")* "}"
    | "REPLACES" "{" (replace_command location ";")* "}"
    | "SURROUNDS" "{" (surround_command location ";")* "}"
    | "FLIPS" "{" (flip_command ";")* "}"
    | "ROTATES" "{" (rotate_command ";")* "}"
    | "MULTIPLYS" "{" (multiply_command ";")* "}"
    | "DIVIDES" "{" (divide_command ";")* "}"
    | "INSERTS" "{" (insert_command location ";")* "}"
    | "DELETES" "{" (delete_command location ";")* "}"
    | motif_command
)

// Commande MOTIF : séquence presse-papier + transformations + collage
motif_command: "MOTIF" "{" clipboard_sequence "}"
clipboard_sequence: copy_cut_command (";" transformation_command)* ";" paste_command
copy_cut_command: ("COPY" | "CUT") location
transformation_command: rotate_command | flip_command | multiply_command | divide_command
paste_command: "PASTE" location

// --- DÉFINITION DES ACTIONS ---
fill_command: "FILL" COLOR
edit_command: "EDIT" COLOR
clear_command: "CLEAR"
replace_command: "REPLACE" color_list COLOR
surround_command: "SURROUND" COLOR

clipboard_command: "COPY" | "CUT" | "PASTE"

// Commandes de transformation (fonctionnent sur le presse-papier)
flip_command: "FLIP" ("HORIZONTAL" | "VERTICAL")
rotate_command: "ROTATE" ("LEFT" | "RIGHT")
multiply_command: "MULTIPLY" NUMBER BOOL?
divide_command: "DIVIDE" NUMBER

// Commandes d'insertion et suppression
resize_command: "RESIZE" GRID_SIZE
insert_command: "INSERT" NUMBER ("ROWS" | "COLUMNS") ("ABOVE" | "BELOW" | "BEFORE" | "AFTER")
delete_command: "DELETE" ("ROWS" | "COLUMNS")
extract_command: "EXTRACT"

// --- DÉFINITION DES LOCATIONS (Coordonnées) ---
location: coordinate_group | rectangle | cell | special_selection

// Groupe de coordonnées multiples
coordinate_group: "(" (rectangle | cell) (" " (rectangle | cell))* ")"

// Sélections spéciales
special_selection: "(" (invert_selection | color_selection) ")"
invert_selection: "INVERT" "(" (coordinate_group | rectangle | cell) ")"
color_selection: "COLOR" color_list "(" (coordinate_group | rectangle | cell) ")"

// --- ÉLÉMENTS DE BASE ---
cell: "[" INT "," INT "]"
rectangle: "[" INT "," INT " " INT "," INT "]"
color_list: COLOR ("," COLOR)*

// --- TERMINAUX ---
GRID_SIZE: INT "x" INT
COLOR: /[0-9]/
INT: /[0-9]+/
NUMBER: /[0-9]+/
BOOL: "true" | "false"

// --- GESTION DES ESPACES ---
%import common.WS
%ignore WS