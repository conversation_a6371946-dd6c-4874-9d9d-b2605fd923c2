import torch
from torch.utils.data import Dataset
import json
import os
import numpy as np
from typing import Dict, List, Tuple, Optional
from src.tokenizer import GrammarTokenizer


class MultiExampleDataset(Dataset):
    """
    Dataset multi-exemples pour DINO-HRM-ARC.
    
    Charge tous les exemples train d'un puzzle simultanément pour permettre
    l'analyse comparative par DINO. Normalise les grilles à 30x30 avec padding.
    
    Returns:
        Dict contenant:
        - train_pairs: List[Tuple[grid, grid]] - Tous les exemples (input, output)
        - test_input: grid - Grille de test
        - puzzle_id: str - Identifiant du puzzle
    """
    
    def __init__(self, data_folder: str, tokenizer: Optional[GrammarTokenizer] = None, 
                 max_grid_size: int = 30):
        """
        Args:
            data_folder: Dossier contenant les puzzles JSON
            tokenizer: Tokenizer pour les programmes (optionnel pour ce dataset)
            max_grid_size: Taille maximale pour le padding des grilles
        """
        self.data = []
        self.tokenizer = tokenizer
        self.max_grid_size = max_grid_size
        
        # Parcourir les fichiers JSON dans le dossier
        for filename in os.listdir(data_folder):
            if filename.endswith('.json'):
                puzzle_id = filename.split('.')[0]
                json_path = os.path.join(data_folder, filename)
                
                # Charger le puzzle avec gestion d'erreur
                try:
                    with open(json_path, 'r') as f:
                        puzzle_data = json.load(f)
                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    print(f"ATTENTION: Fichier JSON corrompu ignoré: {filename} - {e}")
                    continue
                
                # Vérifier qu'il y a au moins un exemple train et un test
                if not puzzle_data.get('train') or not puzzle_data.get('test'):
                    continue
                
                # Extraire tous les exemples train
                train_pairs = []
                for example in puzzle_data['train']:
                    input_grid = np.array(example['input'])
                    output_grid = np.array(example['output'])
                    train_pairs.append((input_grid, output_grid))
                
                # Prendre le premier test_input (ARC a généralement un seul test par puzzle)
                test_input = np.array(puzzle_data['test'][0]['input'])
                
                self.data.append({
                    'train_pairs': train_pairs,
                    'test_input': test_input,
                    'puzzle_id': puzzle_id
                })
    
    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict:
        """
        Retourne un puzzle avec tous ses exemples train normalisés.
        
        Returns:
            Dict avec:
            - train_pairs: List[Tuple[torch.Tensor, torch.Tensor]]
            - test_input: torch.Tensor
            - puzzle_id: str
        """
        item = self.data[idx]
        
        # Normaliser tous les train_pairs
        normalized_train_pairs = []
        for input_grid, output_grid in item['train_pairs']:
            norm_input = self._normalize_grid(input_grid)
            norm_output = self._normalize_grid(output_grid)
            normalized_train_pairs.append((norm_input, norm_output))
        
        # Normaliser le test_input
        normalized_test_input = self._normalize_grid(item['test_input'])
        
        return {
            'train_pairs': normalized_train_pairs,
            'test_input': normalized_test_input,
            'puzzle_id': item['puzzle_id']
        }
    
    def _normalize_grid(self, grid: np.ndarray) -> torch.Tensor:
        """
        Normalise une grille à la taille max_grid_size avec padding.
        
        Args:
            grid: Grille numpy à normaliser
            
        Returns:
            Tensor PyTorch paddé à max_grid_size x max_grid_size
        """
        # Convertir en tensor
        grid_tensor = torch.tensor(grid, dtype=torch.long)
        
        # Obtenir les dimensions actuelles
        h, w = grid_tensor.shape
        
        # Vérifier que la grille n'est pas trop grande
        if h > self.max_grid_size or w > self.max_grid_size:
            # Tronquer si nécessaire (cas rare dans ARC)
            grid_tensor = grid_tensor[:self.max_grid_size, :self.max_grid_size]
            h, w = grid_tensor.shape
        
        # Créer une grille paddée avec des zéros
        padded_grid = torch.zeros((self.max_grid_size, self.max_grid_size), dtype=torch.long)
        
        # Copier la grille originale dans le coin supérieur gauche
        padded_grid[:h, :w] = grid_tensor
        
        return padded_grid
    
    def get_puzzle_stats(self) -> Dict:
        """
        Retourne des statistiques sur le dataset.
        
        Returns:
            Dict avec les statistiques du dataset
        """
        total_puzzles = len(self.data)
        total_train_examples = sum(len(item['train_pairs']) for item in self.data)
        
        # Distribution du nombre d'exemples par puzzle
        examples_per_puzzle = [len(item['train_pairs']) for item in self.data]
        
        # Tailles de grilles
        grid_sizes = []
        for item in self.data:
            for input_grid, output_grid in item['train_pairs']:
                grid_sizes.append(input_grid.shape)
                grid_sizes.append(output_grid.shape)
            grid_sizes.append(item['test_input'].shape)
        
        unique_sizes = list(set(grid_sizes))
        
        return {
            'total_puzzles': total_puzzles,
            'total_train_examples': total_train_examples,
            'avg_examples_per_puzzle': total_train_examples / total_puzzles if total_puzzles > 0 else 0,
            'min_examples_per_puzzle': min(examples_per_puzzle) if examples_per_puzzle else 0,
            'max_examples_per_puzzle': max(examples_per_puzzle) if examples_per_puzzle else 0,
            'unique_grid_sizes': unique_sizes,
            'max_grid_size_used': self.max_grid_size
        }


class MultiExampleDatasetWithPrograms(MultiExampleDataset):
    """
    Version étendue qui inclut aussi les programmes AGI si disponibles.
    
    Utile pour l'entraînement du système complet DINO-HRM.
    """
    
    def __init__(self, data_folder: str, tokenizer: GrammarTokenizer, 
                 max_grid_size: int = 30, max_prog_len: int = 200):
        super().__init__(data_folder, tokenizer, max_grid_size)
        self.max_prog_len = max_prog_len
        
        # Enrichir les données avec les programmes si disponibles
        self._load_programs(data_folder)
    
    def _load_programs(self, data_folder: str):
        """
        Charge les programmes AGI associés aux puzzles.
        """
        for item in self.data:
            puzzle_id = item['puzzle_id']
            program_path = os.path.join(data_folder, f"{puzzle_id}_TEST0_VALID.agi")
            
            if os.path.exists(program_path):
                with open(program_path, 'r') as f:
                    program = f.read().strip()
                
                # Appliquer les mêmes simplifications que l'ancien dataset
                program = self._simplify_program(program)
                item['program'] = program
            else:
                item['program'] = None
    
    def _simplify_program(self, program: str) -> str:
        """
        Applique les simplifications du dataset original.
        """
        # Réutiliser les méthodes de l'ancien dataset
        from src.arc_dataset import ArcDataset
        temp_dataset = ArcDataset.__new__(ArcDataset)  # Créer sans __init__
        
        program = temp_dataset.simplify_transfert(program)
        program = temp_dataset.remove_end_token(program)
        program = temp_dataset.decompose_grouped_commands(program)
        
        return program
    
    def __getitem__(self, idx: int) -> Dict:
        """
        Retourne un puzzle avec programme tokenisé si disponible.
        """
        result = super().__getitem__(idx)
        
        item = self.data[idx]
        if item.get('program') and self.tokenizer:
            # Tokeniser le programme
            prog_tokens = self.tokenizer.tokenize(item['program'])
            prog_tokens = [self.tokenizer.vocab['<sos>']] + prog_tokens + [self.tokenizer.vocab['<eos>']]
            
            # Padding/truncation
            if len(prog_tokens) > self.max_prog_len:
                prog_tokens = prog_tokens[:self.max_prog_len]
            else:
                prog_tokens = prog_tokens + [self.tokenizer.vocab['<pad>']] * (self.max_prog_len - len(prog_tokens))
            
            result['program_tokens'] = torch.tensor(prog_tokens, dtype=torch.long)
            result['program_text'] = item['program']
        else:
            result['program_tokens'] = None
            result['program_text'] = None
        
        return result