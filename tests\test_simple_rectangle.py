import numpy as np
from scipy.ndimage import label

def all_rectangles(mat):
    mat = np.asarray(mat)
    results = {}
    for val in np.unique(mat):
        mask = (mat == val)
        labeled, n = label(mask)
        rects = []
        for k in range(1, n+1):
            rows, cols = np.where(labeled == k)
            rmin, rmax = rows.min(), rows.max()
            cmin, cmax = cols.min(), cols.max()
            # Vérification : si le rectangle extrait est bien plein de val
            # ET qu'il fait au moins 2x2 (pas juste une cellule)
            if np.all(mat[rmin:rmax+1, cmin:cmax+1] == val) and (rmax > rmin or cmax > cmin):
                rects.append((rmin, cmin, rmax, cmax))
        if rects:
            results[int(val)] = [f"[{r1},{c1} {r2},{c2}]"
                               for r1, c1, r2, c2 in rects]
    return results

# Test sur le premier exemple d'entraînement de 0dfd9992
M = [[3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 0, 0, 0, 0, 0, 5], [2, 5, 4, 5, 2, 1, 2, 5, 0, 0, 2, 1, 2, 5, 4, 0, 0, 0, 0, 0, 4], [3, 6, 5, 6, 3, 2, 3, 0, 0, 0, 0, 2, 3, 6, 5, 0, 0, 0, 0, 0, 5], [6, 3, 2, 3, 6, 5, 6, 0, 0, 0, 0, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 0, 0, 0, 0, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 0, 0, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 0, 0, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 0, 0, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 0, 0, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [3, 6, 5, 6, 0, 0, 0, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [2, 5, 4, 5, 0, 0, 0, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4], [3, 6, 5, 6, 0, 0, 0, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5], [6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2, 3, 6, 5, 6, 3, 2], [5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1, 2, 5, 4, 5, 2, 1]]

zones = all_rectangles(M)
for val, rects in zones.items():
    print(f"Valeur {val} : {rects}")