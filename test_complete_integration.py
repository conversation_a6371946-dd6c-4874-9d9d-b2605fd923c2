#!/usr/bin/env python3
"""
Script de test pour l'intégration complète DINO-HRM-ARC.

Valide que tous les composants fonctionnent ensemble et que
le pipeline complet peut résoudre des puzzles ARC.
"""

import sys
import os
sys.path.append('.')

import torch
import numpy as np
from integration.complete_pipeline_test import (
    IntegrationConfig, IntegratedARCSolver, IntegrationTester
)
from models.arc_solver import SolutionStatus


def test_integration_config():
    """Test de la configuration d'intégration."""
    print("=== Test IntegrationConfig ===")
    
    # Configuration par défaut
    config = IntegrationConfig()
    
    print(f"Max test puzzles: {config.max_test_puzzles}")
    print(f"Timeout per puzzle: {config.timeout_per_puzzle}")
    print(f"Target success rate: {config.target_success_rate}")
    print(f"Device: {config.device}")
    
    # Configuration personnalisée
    custom_config = IntegrationConfig(
        max_test_puzzles=5,
        timeout_per_puzzle=10.0,
        target_success_rate=0.2,
        device="cpu"
    )
    
    assert custom_config.max_test_puzzles == 5, "Max test puzzles incorrect"
    assert custom_config.timeout_per_puzzle == 10.0, "Timeout incorrect"
    assert custom_config.target_success_rate == 0.2, "Target success rate incorrect"
    
    print("✓ IntegrationConfig OK")


def test_integrated_solver_creation():
    """Test de création du solver intégré."""
    print("\n=== Test IntegratedARCSolver ===")
    
    config = IntegrationConfig(device="cpu")
    
    # Créer le solver intégré
    solver = IntegratedARCSolver(config)
    
    print(f"Solver type: {type(solver).__name__}")
    print(f"DINO model: {type(solver.dino).__name__}")
    print(f"Generalizer: {type(solver.generalizer).__name__}")
    print(f"HRM resolver: {type(solver.hrm).__name__}")
    print(f"Validator: {type(solver.validator).__name__}")
    
    # Vérifications
    assert hasattr(solver, 'dino'), "Doit avoir un modèle DINO"
    assert hasattr(solver, 'generalizer'), "Doit avoir un généralisateur"
    assert hasattr(solver, 'hrm'), "Doit avoir un résolveur HRM"
    assert hasattr(solver, 'validator'), "Doit avoir un validateur"
    
    print("✓ IntegratedARCSolver OK")


def test_synthetic_puzzle_creation():
    """Test de création de puzzles synthétiques."""
    print("\n=== Test création puzzles synthétiques ===")
    
    config = IntegrationConfig(max_test_puzzles=5, device="cpu")
    tester = IntegrationTester(config)
    
    # Créer des puzzles synthétiques
    synthetic_puzzles = tester._create_synthetic_puzzles()
    
    print(f"Nombre de puzzles créés: {len(synthetic_puzzles)}")
    
    if synthetic_puzzles:
        puzzle = synthetic_puzzles[0]
        print(f"Clés du puzzle: {list(puzzle.keys())}")
        print(f"Puzzle ID: {puzzle['puzzle_id']}")
        print(f"Train pairs: {len(puzzle['train_pairs'])}")
        print(f"Test input shape: {puzzle['test_input'].shape}")
        
        # Vérifications
        assert 'train_pairs' in puzzle, "Doit avoir des train_pairs"
        assert 'test_input' in puzzle, "Doit avoir un test_input"
        assert 'puzzle_id' in puzzle, "Doit avoir un puzzle_id"
        assert len(puzzle['train_pairs']) > 0, "Doit avoir au moins une paire train"
    
    print("✓ Création puzzles synthétiques OK")


def test_single_puzzle_integration():
    """Test d'intégration sur un puzzle unique."""
    print("\n=== Test intégration puzzle unique ===")
    
    config = IntegrationConfig(timeout_per_puzzle=15.0, device="cpu")
    solver = IntegratedARCSolver(config)
    
    # Créer un puzzle de test
    train_pairs = [
        (torch.randint(0, 3, (4, 4)), torch.randint(0, 3, (4, 4))),
        (torch.randint(0, 3, (4, 4)), torch.randint(0, 3, (4, 4)))
    ]
    
    test_input = torch.randint(0, 3, (4, 4))
    
    puzzle_data = {
        'train_pairs': train_pairs,
        'test_input': test_input,
        'puzzle_id': 'integration_test_puzzle'
    }
    
    print(f"Test puzzle: {puzzle_data['puzzle_id']}")
    print(f"Train pairs: {len(puzzle_data['train_pairs'])}")
    print(f"Test input shape: {puzzle_data['test_input'].shape}")
    
    # Résoudre le puzzle
    result = solver.solve_puzzle(puzzle_data)
    
    print(f"\nRésultat:")
    print(f"  Statut: {result.status.value}")
    print(f"  Catégorie: {result.detected_category}")
    print(f"  Confiance: {result.confidence:.3f}")
    print(f"  Temps total: {result.execution_time:.3f}s")
    print(f"  Temps DINO: {result.dino_time:.3f}s")
    print(f"  Temps HRM: {result.hrm_time:.3f}s")
    print(f"  Temps validation: {result.validation_time:.3f}s")
    
    if result.generated_scenario:
        print(f"  Scénario: {result.generated_scenario}")
    
    if result.final_command:
        print(f"  Commande: {result.final_command}")
    
    if result.errors:
        print(f"  Erreurs: {result.errors}")
    
    # Vérifications
    assert isinstance(result.status, SolutionStatus), "Statut doit être un SolutionStatus"
    assert result.execution_time > 0, "Temps d'exécution doit être positif"
    assert result.detected_category != "", "Catégorie doit être détectée"
    assert 0 <= result.confidence <= 1, "Confiance doit être entre 0 et 1"
    
    print("✓ Intégration puzzle unique OK")


def test_integration_tester():
    """Test du testeur d'intégration."""
    print("\n=== Test IntegrationTester ===")
    
    config = IntegrationConfig(
        max_test_puzzles=3,  # Très limité pour les tests
        timeout_per_puzzle=10.0,
        target_success_rate=0.01,  # 1% seulement
        device="cpu"
    )
    
    tester = IntegrationTester(config)
    
    print(f"Tester créé avec config:")
    print(f"  Max puzzles: {config.max_test_puzzles}")
    print(f"  Timeout: {config.timeout_per_puzzle}s")
    print(f"  Target success: {config.target_success_rate:.1%}")
    
    # Vérifications
    assert hasattr(tester, 'solver'), "Doit avoir un solver"
    assert hasattr(tester, 'global_metrics'), "Doit avoir des métriques"
    assert tester.global_metrics['total_puzzles'] == 0, "Métriques initiales incorrectes"
    
    print("✓ IntegrationTester OK")


def test_mini_integration_run():
    """Test d'exécution d'intégration minimale."""
    print("\n=== Test exécution intégration minimale ===")
    
    config = IntegrationConfig(
        max_test_puzzles=2,  # Seulement 2 puzzles
        timeout_per_puzzle=8.0,
        target_success_rate=0.01,  # Très bas pour passer
        target_avg_time=20.0,
        device="cpu"
    )
    
    tester = IntegrationTester(config)
    
    print(f"Lancement des tests d'intégration...")
    
    # Exécuter les tests
    report = tester.run_integration_tests()
    
    print(f"\nRapport reçu:")
    print(f"  Clés: {list(report.keys())}")
    
    if 'summary' in report:
        summary = report['summary']
        print(f"  Total puzzles: {summary.get('total_puzzles', 0)}")
        print(f"  Success rate: {summary.get('success_rate', 0):.1%}")
        print(f"  Avg time: {summary.get('avg_time_per_puzzle', 0):.2f}s")
        print(f"  Overall grade: {summary.get('overall_grade', 'UNKNOWN')}")
        
        # Vérifications
        assert summary['total_puzzles'] > 0, "Doit avoir testé au moins un puzzle"
        assert 0 <= summary['success_rate'] <= 1, "Success rate doit être entre 0 et 1"
        assert summary['avg_time_per_puzzle'] > 0, "Temps moyen doit être positif"
        assert summary['overall_grade'] in ['PASS', 'FAIL'], "Grade doit être PASS ou FAIL"
    
    print("✓ Exécution intégration minimale OK")


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    config = IntegrationConfig(device="cpu")
    
    # Requirement 7.3: Intégration des modèles entraînés
    solver = IntegratedARCSolver(config)
    
    # Vérifier que les composants sont intégrés
    assert hasattr(solver, 'dino'), "DINO doit être intégré"
    assert hasattr(solver, 'hrm'), "HRM doit être intégré"
    assert hasattr(solver, 'generalizer'), "Généralisateur doit être intégré"
    assert hasattr(solver, 'validator'), "Validateur doit être intégré"
    
    print("✓ Requirement 7.3: Intégration des modèles OK")
    
    # Vérifier que le pipeline fonctionne end-to-end
    tester = IntegrationTester(IntegrationConfig(max_test_puzzles=1, device="cpu"))
    assert hasattr(tester, 'solver'), "Pipeline doit être fonctionnel"
    
    print("✓ Pipeline complet fonctionnel OK")


def main():
    """Fonction principale de test."""
    print("Test de l'intégration complète DINO-HRM-ARC")
    print("=" * 55)
    
    success = True
    
    try:
        test_integration_config()
        test_integrated_solver_creation()
        test_synthetic_puzzle_creation()
        test_single_puzzle_integration()
        test_integration_tester()
        test_mini_integration_run()
        test_requirements_compliance()
        
        print("\n🎉 Tous les tests d'intégration sont passés!")
        print("Le pipeline complet DINO-HRM-ARC est opérationnel")
        
    except Exception as e:
        print(f"\n💥 Erreur durant les tests d'intégration: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)