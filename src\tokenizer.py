"""
Module de tokenisation grammaticale pour les programmes ARC.
Ce tokenizer convertit les commandes textuelles en séquences d'entiers (tokens)
et vice-versa, en se basant sur un vocabulaire défini qui correspond
aux commandes unifiées du projet.
"""
import re

class GrammarTokenizer:
    """
    Tokenizer basé sur un vocabulaire grammatical fixe pour les commandes ARC.
    """
    def __init__(self):
        """Initialise le tokenizer avec un vocabulaire nettoyé et optimisé."""
        self.vocab = {
            # --- Jetons Spéciaux ---
            '<pad>': 0, '<sos>': 1, '<eos>': 2, '<unk>': 3,

            # --- Délimiteurs et Symboles ---
            ' ': 4, ',': 5, ';': 6, '(': 7, ')': 8,
            '[': 9, ']': 10, '{': 11, '}': 12, 'x': 13,

            # === Actions de Base ===
            'CLEAR': 14, 
            'FILL': 15, 
            'SURROUND': 16, 
            'REPLACE': 17, 
            'EDIT': 18,
            'FLOODFILL': 19,

            # === Modifications Structurelles ===
            'INSERT': 20, 
            'DELETE': 21, 
            'EXTRACT': 22,
            'RESIZE': 23,

            # === Transformations de Motifs ===
            'FLIP': 24, 
            'ROTATE': 25,
            'MULTIPLY': 26,
            'DIVIDE': 27,

            # === Presse-papiers ===
            'COPY': 28, 
            'CUT': 29, 
            'PASTE': 30,

            # === Sélections Spéciales ===
            'INVERT': 31, 
            'COLOR': 32,

            # === Commandes Système et Blocs ===
            'INIT': 33,
            'TRANSFERT': 34,
            'MOTIF': 35,

            # === Paramètres ===
            # --- Directions et Positions ---
            'HORIZONTAL': 36, 'VERTICAL': 37,
            'LEFT': 38, 'RIGHT': 39,
            'ABOVE': 40, 'BELOW': 41,
            'BEFORE': 42, 'AFTER': 43,
            # --- Types d'éléments ---
            'ROWS': 44, 'COLUMNS': 45,
            # --- Booléens ---
            'true': 46, 'false': 47,
        }
        
        # --- Ajout dynamique des chiffres 0-30 (pour grilles ARC) ---
        for i in range(31):  # 0-30 pour couvrir les tailles de grilles ARC
            self.vocab[str(i)] = len(self.vocab)
        
        self.reverse_vocab = {idx: token for token, idx in self.vocab.items()}
        self.unk_token_id = self.vocab['<unk>']

    def tokenize(self, program: str) -> list[int]:
        """
        Tokenize un programme en une séquence d'IDs de vocabulaire.
        Regex améliorée pour éviter les tokens inconnus.
        """
        if not program:
            return []

        # Regex optimisée pour capturer tous les éléments valides
        tokens = re.findall(r'\d+|[A-Z_]+|true|false|[^\w\s]|\s+', program)
        
        token_ids = []
        for token in tokens:
            if token in self.vocab:
                token_ids.append(self.vocab[token])
            elif token.isdigit():
                num = int(token)
                # Limiter aux nombres 0-30 (taille max ARC)
                if num <= 30:
                    token_ids.append(self.vocab[str(num)])
                else:
                    # Utiliser 30 comme fallback pour les nombres > 30
                    token_ids.append(self.vocab['30'])
            elif token.isspace():
                # Ignorer les espaces multiples, garder seulement un espace
                if token_ids and token_ids[-1] != self.vocab[' ']:
                    token_ids.append(self.vocab[' '])
            else:
                # Token vraiment inconnu
                token_ids.append(self.unk_token_id)
            
        return token_ids

    def detokenize(self, token_ids: list[int]) -> str:
        """
        Convertit une séquence d'IDs en une chaîne de programme lisible.
        """
        tokens = [self.reverse_vocab.get(idx, '<unk>') for idx in token_ids]
        
        # Filtrer les tokens spéciaux
        filtered_tokens = []
        for token in tokens:
            if token not in ['<pad>', '<sos>', '<eos>']:
                filtered_tokens.append(token)
        
        # Reconstruire le programme
        program_str = ''.join(filtered_tokens)
        
        # Nettoyer les espaces multiples
        program_str = re.sub(r'\s+', ' ', program_str)
        
        return program_str.strip()

