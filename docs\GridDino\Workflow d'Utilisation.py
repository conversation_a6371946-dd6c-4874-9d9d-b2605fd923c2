# Initialisation
model = GridDINO(grid_size=30, vocab_size=10)
pretrainer = GridDINOPretrainer(model)
optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4)

# Boucle d'entraînement
for grids in dataloader:
    # Forward pass
    outputs = pretrainer(grids)
    
    # Générer une vue augmentée
    augmented_grids = random_rotation(grids)
    outputs_aug = pretrainer(augmented_grids)
    
    # Calcul des pertes
    recon_loss = outputs['recon_loss']
    contrastive_loss = contrastive_loss_fn(
        outputs['pattern_features'],
        outputs_aug['pattern_features']
    )
    
    # Perte totale
    loss = recon_loss + 0.5 * contrastive_loss
    
    # Rétropropagation
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()