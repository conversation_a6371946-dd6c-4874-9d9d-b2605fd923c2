#!/usr/bin/env python3
"""
Test du détecteur de rectangles sur les puzzles mosaïques confirmés
"""

import numpy as np
import json
import os
from scipy.ndimage import label
from typing import Dict, List, Any

def all_rectangles(mat):
    """
    Détecte tous les rectangles pleins dans une matrice
    """
    mat = np.asarray(mat)
    results = {}
    
    for val in np.unique(mat):
        mask = (mat == val)
        labeled, n = label(mask)
        rects = []
        
        for k in range(1, n+1):
            rows, cols = np.where(labeled == k)
            rmin, rmax = rows.min(), rows.max()
            cmin, cmax = cols.min(), cols.max()
            
            # Vérification : si le rectangle extrait est bien plein de val
            # ET qu'il fait au moins 2x2 (pas juste une cellule)
            if np.all(mat[rmin:rmax+1, cmin:cmax+1] == val) and (rmax > rmin or cmax > cmin):
                rects.append((rmin, cmin, rmax, cmax))
        
        if rects:
            results[int(val)] = [f"[{r1},{c1} {r2},{c2}]" 
                               for r1, c1, r2, c2 in rects]
    
    return results

def load_arc_data():
    """Charge les données ARC"""
    training_path = "arcdata/training"
    evaluation_path = "arcdata/evaluation"
    
    data = {}
    errors = []
    
    # Charger training
    if os.path.exists(training_path):
        for filename in os.listdir(training_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]  # Remove .json
                try:
                    with open(os.path.join(training_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except Exception as e:
                    errors.append(f"Erreur training/{filename}: {e}")
    
    # Charger evaluation
    if os.path.exists(evaluation_path):
        for filename in os.listdir(evaluation_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]  # Remove .json
                try:
                    with open(os.path.join(evaluation_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except Exception as e:
                    errors.append(f"Erreur evaluation/{filename}: {e}")
    
    if errors:
        print(f"⚠️  {len(errors)} fichiers avec erreurs de parsing:")
        for error in errors[:5]:  # Afficher seulement les 5 premiers
            print(f"   {error}")
        if len(errors) > 5:
            print(f"   ... et {len(errors) - 5} autres erreurs")
    
    return data

def get_mosaic_puzzle_ids():
    """Récupère les IDs des puzzles mosaïques"""
    mosaic_ids = []
    
    try:
        # Essayer différents encodages
        encodings = ['utf-8', 'latin-1', 'cp1252']
        content = None
        
        for encoding in encodings:
            try:
                with open("AnalysesGrilles/final_mosaic_puzzle_ids.txt", 'r', encoding=encoding) as f:
                    content = f.read()
                    break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("Impossible de lire le fichier avec les encodages testés")
            return mosaic_ids
        
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # Extraire l'ID (premier mot avant l'espace ou #)
                task_id = line.split()[0]
                if len(task_id) == 8:  # Format ARC standard
                    mosaic_ids.append(task_id)
                    
    except Exception as e:
        print(f"Erreur lecture des IDs mosaïques: {e}")
    
    return mosaic_ids

def test_rectangle_detector():
    """Test principal du détecteur de rectangles"""
    print("=== TEST DU DÉTECTEUR DE RECTANGLES SUR PUZZLES MOSAÏQUES ===\n")
    
    # Charger les données
    print("Chargement des données ARC...")
    arc_data = load_arc_data()
    print(f"Données chargées: {len(arc_data)} tâches")
    
    # Récupérer les IDs mosaïques
    mosaic_ids = get_mosaic_puzzle_ids()
    print(f"Puzzles mosaïques identifiés: {len(mosaic_ids)}")
    
    results = {}
    successful_tests = 0
    failed_tests = 0
    
    for task_id in mosaic_ids:
        if task_id not in arc_data:
            print(f"⚠️  Tâche {task_id} non trouvée dans les données")
            failed_tests += 1
            continue
        
        task_data = arc_data[task_id]
        print(f"\n--- Test sur {task_id} ---")
        
        task_results = {
            'task_id': task_id,
            'train_examples': [],
            'test_examples': []
        }
        
        # Tester sur les exemples d'entraînement
        for i, example in enumerate(task_data.get('train', [])):
            input_grid = np.array(example['input'])
            output_grid = np.array(example['output'])
            
            print(f"  Exemple train {i+1}:")
            print(f"    Input: {input_grid.shape}")
            
            try:
                # Test sur input
                input_rects = all_rectangles(input_grid)
                print(f"    Rectangles input: {len(sum(input_rects.values(), []))} total")
                
                # Test sur output
                output_rects = all_rectangles(output_grid)
                print(f"    Rectangles output: {len(sum(output_rects.values(), []))} total")
                
                task_results['train_examples'].append({
                    'example_id': i,
                    'input_shape': input_grid.shape,
                    'output_shape': output_grid.shape,
                    'input_rectangles': input_rects,
                    'output_rectangles': output_rects,
                    'input_rect_count': len(sum(input_rects.values(), [])),
                    'output_rect_count': len(sum(output_rects.values(), []))
                })
                
            except Exception as e:
                print(f"    ❌ Erreur: {e}")
                task_results['train_examples'].append({
                    'example_id': i,
                    'error': str(e)
                })
        
        # Tester sur les exemples de test
        for i, example in enumerate(task_data.get('test', [])):
            input_grid = np.array(example['input'])
            
            print(f"  Exemple test {i+1}:")
            print(f"    Input: {input_grid.shape}")
            
            try:
                input_rects = all_rectangles(input_grid)
                print(f"    Rectangles input: {len(sum(input_rects.values(), []))} total")
                
                task_results['test_examples'].append({
                    'example_id': i,
                    'input_shape': input_grid.shape,
                    'input_rectangles': input_rects,
                    'input_rect_count': len(sum(input_rects.values(), []))
                })
                
            except Exception as e:
                print(f"    ❌ Erreur: {e}")
                task_results['test_examples'].append({
                    'example_id': i,
                    'error': str(e)
                })
        
        results[task_id] = task_results
        successful_tests += 1
    
    # Sauvegarder les résultats
    output_file = "rectangle_detector_mosaic_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n=== RÉSUMÉ ===")
    print(f"Tests réussis: {successful_tests}")
    print(f"Tests échoués: {failed_tests}")
    print(f"Résultats sauvegardés dans: {output_file}")
    
    # Statistiques globales
    total_rectangles = 0
    total_grids = 0
    
    for task_id, task_result in results.items():
        for example in task_result['train_examples']:
            if 'input_rect_count' in example:
                total_rectangles += example['input_rect_count']
                total_grids += 1
            if 'output_rect_count' in example:
                total_rectangles += example['output_rect_count']
                total_grids += 1
        
        for example in task_result['test_examples']:
            if 'input_rect_count' in example:
                total_rectangles += example['input_rect_count']
                total_grids += 1
    
    if total_grids > 0:
        avg_rectangles = total_rectangles / total_grids
        print(f"Moyenne de rectangles par grille: {avg_rectangles:.2f}")
        print(f"Total rectangles détectés: {total_rectangles}")
        print(f"Total grilles analysées: {total_grids}")

if __name__ == "__main__":
    test_rectangle_detector()