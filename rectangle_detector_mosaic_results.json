{"0dfd9992": {"task_id": "0dfd9992", "train_examples": [{"example_id": 0, "input_shape": [21, 21], "output_shape": [21, 21], "input_rectangles": {"0": ["[4,15 6,19]", "[11,3 13,4]", "[16,4 18,6]"]}, "output_rectangles": {}, "input_rect_count": 3, "output_rect_count": 0}, {"example_id": 1, "input_shape": [21, 21], "output_shape": [21, 21], "input_rectangles": {"0": ["[3,4 6,8]", "[8,0 10,4]", "[13,16 15,17]"], "3": ["[1,5 2,5]", "[1,12 2,12]", "[1,19 2,19]", "[5,1 5,2]", "[8,5 9,5]", "[8,12 9,12]", "[12,1 12,2]", "[12,8 12,9]", "[12,15 12,16]", "[15,5 16,5]", "[15,12 16,12]", "[15,19 16,19]", "[19,1 19,2]", "[19,8 19,9]", "[19,15 19,16]"], "4": ["[1,4 2,4]", "[1,6 2,6]", "[1,11 2,11]", "[1,13 2,13]", "[1,18 2,18]", "[1,20 2,20]", "[4,1 4,2]", "[6,1 6,2]", "[8,6 9,6]", "[8,11 9,11]", "[8,13 9,13]", "[8,20 9,20]", "[11,1 11,2]", "[11,8 11,9]", "[11,15 11,16]", "[13,1 13,2]", "[13,8 13,9]", "[15,4 16,4]", "[15,6 16,6]", "[15,11 16,11]", "[15,13 16,13]", "[15,18 16,18]", "[15,20 16,20]", "[18,1 18,2]", "[18,8 18,9]", "[18,15 18,16]", "[20,1 20,2]", "[20,8 20,9]", "[20,15 20,16]"], "5": ["[1,1 2,2]", "[1,8 2,9]", "[1,15 2,16]", "[8,8 9,9]", "[8,15 9,16]", "[15,1 16,2]", "[15,8 16,9]"], "7": ["[0,1 0,2]", "[0,8 0,9]", "[0,15 0,16]", "[1,0 2,0]", "[1,3 2,3]", "[1,7 2,7]", "[1,10 2,10]", "[1,14 2,14]", "[1,17 2,17]", "[3,1 3,2]", "[7,1 7,2]", "[7,8 7,9]", "[7,15 7,16]", "[8,7 9,7]", "[8,10 9,10]", "[8,14 9,14]", "[10,8 10,9]", "[10,15 10,16]", "[14,1 14,2]", "[14,8 14,9]", "[15,0 16,0]", "[15,3 16,3]", "[15,7 16,7]", "[15,10 16,10]", "[15,14 16,14]", "[17,1 17,2]", "[17,8 17,9]", "[17,15 17,16]"]}, "output_rectangles": {"3": ["[1,5 2,5]", "[1,12 2,12]", "[1,19 2,19]", "[5,1 5,2]", "[5,8 5,9]", "[5,15 5,16]", "[8,5 9,5]", "[8,12 9,12]", "[8,19 9,19]", "[12,1 12,2]", "[12,8 12,9]", "[12,15 12,16]", "[15,5 16,5]", "[15,12 16,12]", "[15,19 16,19]", "[19,1 19,2]", "[19,8 19,9]", "[19,15 19,16]"], "4": ["[1,4 2,4]", "[1,6 2,6]", "[1,11 2,11]", "[1,13 2,13]", "[1,18 2,18]", "[1,20 2,20]", "[4,1 4,2]", "[4,8 4,9]", "[4,15 4,16]", "[6,1 6,2]", "[6,8 6,9]", "[6,15 6,16]", "[8,4 9,4]", "[8,6 9,6]", "[8,11 9,11]", "[8,13 9,13]", "[8,18 9,18]", "[8,20 9,20]", "[11,1 11,2]", "[11,8 11,9]", "[11,15 11,16]", "[13,1 13,2]", "[13,8 13,9]", "[13,15 13,16]", "[15,4 16,4]", "[15,6 16,6]", "[15,11 16,11]", "[15,13 16,13]", "[15,18 16,18]", "[15,20 16,20]", "[18,1 18,2]", "[18,8 18,9]", "[18,15 18,16]", "[20,1 20,2]", "[20,8 20,9]", "[20,15 20,16]"], "5": ["[1,1 2,2]", "[1,8 2,9]", "[1,15 2,16]", "[8,1 9,2]", "[8,8 9,9]", "[8,15 9,16]", "[15,1 16,2]", "[15,8 16,9]", "[15,15 16,16]"], "7": ["[0,1 0,2]", "[0,8 0,9]", "[0,15 0,16]", "[1,0 2,0]", "[1,3 2,3]", "[1,7 2,7]", "[1,10 2,10]", "[1,14 2,14]", "[1,17 2,17]", "[3,1 3,2]", "[3,8 3,9]", "[3,15 3,16]", "[7,1 7,2]", "[7,8 7,9]", "[7,15 7,16]", "[8,0 9,0]", "[8,3 9,3]", "[8,7 9,7]", "[8,10 9,10]", "[8,14 9,14]", "[8,17 9,17]", "[10,1 10,2]", "[10,8 10,9]", "[10,15 10,16]", "[14,1 14,2]", "[14,8 14,9]", "[14,15 14,16]", "[15,0 16,0]", "[15,3 16,3]", "[15,7 16,7]", "[15,10 16,10]", "[15,14 16,14]", "[15,17 16,17]", "[17,1 17,2]", "[17,8 17,9]", "[17,15 17,16]"]}, "input_rect_count": 82, "output_rect_count": 99}, {"example_id": 2, "input_shape": [21, 21], "output_shape": [21, 21], "input_rectangles": {"0": ["[2,0 3,4]", "[4,14 5,16]", "[5,4 9,7]", "[5,9 8,11]", "[14,1 18,5]"]}, "output_rectangles": {}, "input_rect_count": 5, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [21, 21], "input_rectangles": {"0": ["[1,9 3,13]", "[8,4 10,7]", "[12,14 15,15]", "[15,0 16,1]", "[15,4 19,6]"], "3": ["[0,6 0,7]", "[0,15 0,16]", "[4,6 4,7]", "[4,15 4,16]", "[6,0 7,0]", "[6,4 7,4]", "[6,9 7,9]", "[6,13 7,13]", "[6,18 7,18]", "[9,15 9,16]", "[13,6 13,7]", "[15,9 16,9]", "[15,13 16,13]", "[15,18 16,18]", "[18,15 18,16]"], "6": ["[6,6 7,7]", "[6,15 7,16]", "[15,7 16,7]"], "8": ["[2,6 2,7]", "[2,15 2,16]", "[5,6 5,7]", "[5,15 5,16]", "[6,2 7,2]", "[6,5 7,5]", "[6,8 7,8]", "[6,11 7,11]", "[6,14 7,14]", "[6,17 7,17]", "[6,20 7,20]", "[8,15 8,16]", "[11,6 11,7]", "[11,15 11,16]", "[14,6 14,7]", "[15,2 16,2]", "[15,8 16,8]", "[15,11 16,11]", "[15,17 16,17]", "[15,20 16,20]", "[17,15 17,16]", "[20,6 20,7]", "[20,15 20,16]"], "9": ["[1,6 1,7]", "[1,15 1,16]", "[3,6 3,7]", "[3,15 3,16]", "[6,1 7,1]", "[6,3 7,3]", "[6,10 7,10]", "[6,12 7,12]", "[6,19 7,19]", "[10,15 10,16]", "[12,6 12,7]", "[15,3 16,3]", "[15,10 16,10]", "[15,12 16,12]", "[15,19 16,19]", "[19,15 19,16]"]}, "input_rect_count": 62}]}, "29ec7d0e": {"task_id": "29ec7d0e", "train_examples": [{"example_id": 0, "input_shape": [18, 18], "output_shape": [18, 18], "input_rectangles": {"0": ["[0,3 2,4]", "[2,10 3,11]", "[4,6 5,7]", "[5,11 6,13]", "[10,0 12,3]"]}, "output_rectangles": {}, "input_rect_count": 5, "output_rect_count": 0}, {"example_id": 1, "input_shape": [18, 18], "output_shape": [18, 18], "input_rectangles": {"0": ["[0,7 3,9]", "[4,11 5,13]", "[5,3 7,6]", "[11,7 13,9]", "[11,12 13,15]"], "1": ["[12,10 12,11]", "[12,16 12,17]", "[14,12 17,12]"]}, "output_rectangles": {}, "input_rect_count": 8, "output_rect_count": 0}, {"example_id": 2, "input_shape": [18, 18], "output_shape": [18, 18], "input_rectangles": {"0": ["[5,1 8,2]"], "1": ["[7,16 7,17]"]}, "output_rectangles": {}, "input_rect_count": 2, "output_rect_count": 0}, {"example_id": 3, "input_shape": [18, 18], "output_shape": [18, 18], "input_rectangles": {"0": ["[3,14 5,15]", "[13,6 14,7]", "[15,4 16,5]"]}, "output_rectangles": {}, "input_rect_count": 3, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [18, 18], "input_rectangles": {"0": ["[0,13 2,15]", "[5,3 8,5]", "[13,1 16,3]"], "1": ["[0,16 0,17]"]}, "input_rect_count": 4}]}, "484b58aa": {"task_id": "484b58aa", "train_examples": [{"example_id": 0, "input_shape": [29, 29], "output_shape": [29, 29], "input_rectangles": {"0": ["[6,24 11,26]", "[21,13 23,16]"]}, "output_rectangles": {}, "input_rect_count": 2, "output_rect_count": 0}, {"example_id": 1, "input_shape": [29, 29], "output_shape": [29, 29], "input_rectangles": {"0": ["[1,0 4,3]", "[13,9 14,13]", "[16,4 21,6]"], "2": ["[0,4 0,5]", "[0,8 1,8]", "[0,14 2,14]", "[0,20 3,20]", "[5,2 7,2]", "[15,10 15,11]"], "3": ["[2,4 3,4]"], "5": ["[0,6 1,6]", "[0,21 0,22]", "[1,12 2,12]", "[1,27 1,28]", "[2,18 3,18]", "[3,24 4,24]", "[5,9 5,10]", "[6,0 7,0]", "[6,15 6,16]", "[7,6 8,6]", "[7,21 7,22]", "[8,12 9,12]", "[8,27 8,28]", "[9,18 10,18]", "[10,24 11,24]", "[11,3 11,4]", "[12,9 12,10]", "[13,0 14,0]", "[13,15 13,16]", "[14,6 15,6]", "[15,12 16,12]", "[15,27 15,28]", "[16,18 17,18]", "[17,24 18,24]", "[19,9 19,10]", "[20,0 21,0]", "[20,15 20,16]", "[21,21 21,22]", "[22,12 23,12]", "[22,27 22,28]", "[23,18 24,18]", "[24,24 25,24]", "[25,3 25,4]", "[26,9 26,10]", "[27,0 28,0]", "[27,15 27,16]", "[28,21 28,22]"], "7": ["[3,5 4,5]", "[4,11 5,11]", "[5,17 6,17]", "[6,23 7,23]", "[10,5 11,5]", "[11,11 12,11]", "[12,17 13,17]", "[18,11 19,11]", "[19,17 20,17]", "[20,23 21,23]", "[24,5 25,5]", "[25,11 26,11]", "[26,17 27,17]", "[27,23 28,23]"]}, "output_rectangles": {"2": ["[0,4 0,5]", "[0,8 1,8]", "[0,14 2,14]", "[0,20 3,20]"], "5": ["[0,6 1,6]", "[0,21 0,22]", "[1,12 2,12]", "[1,27 1,28]", "[2,18 3,18]", "[3,24 4,24]", "[4,3 4,4]", "[5,9 5,10]", "[6,0 7,0]", "[6,15 6,16]", "[7,6 8,6]", "[7,21 7,22]", "[8,12 9,12]", "[8,27 8,28]", "[9,18 10,18]", "[10,24 11,24]", "[11,3 11,4]", "[12,9 12,10]", "[13,0 14,0]", "[13,15 13,16]", "[14,6 15,6]", "[14,21 14,22]", "[15,12 16,12]", "[15,27 15,28]", "[16,18 17,18]", "[17,24 18,24]", "[18,3 18,4]", "[19,9 19,10]", "[20,0 21,0]", "[20,15 20,16]", "[21,6 22,6]", "[21,21 21,22]", "[22,12 23,12]", "[22,27 22,28]", "[23,18 24,18]", "[24,24 25,24]", "[25,3 25,4]", "[26,9 26,10]", "[27,0 28,0]", "[27,15 27,16]", "[28,21 28,22]"], "7": ["[3,5 4,5]", "[4,11 5,11]", "[5,17 6,17]", "[6,23 7,23]", "[10,5 11,5]", "[11,11 12,11]", "[12,17 13,17]", "[13,23 14,23]", "[17,5 18,5]", "[18,11 19,11]", "[19,17 20,17]", "[20,23 21,23]", "[24,5 25,5]", "[25,11 26,11]", "[26,17 27,17]", "[27,23 28,23]"]}, "input_rect_count": 61, "output_rect_count": 61}, {"example_id": 2, "input_shape": [29, 29], "output_shape": [29, 29], "input_rectangles": {"0": ["[4,6 10,12]"]}, "output_rectangles": {}, "input_rect_count": 1, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [29, 29], "input_rectangles": {"0": ["[0,6 4,9]", "[1,14 7,16]", "[12,13 13,15]"], "2": ["[0,2 1,2]", "[0,20 1,20]", "[0,23 0,24]", "[3,2 4,2]", "[3,11 3,12]", "[3,20 4,20]", "[3,26 4,26]", "[6,8 7,8]", "[6,17 6,18]", "[6,26 7,26]", "[9,2 10,2]", "[9,5 9,6]", "[9,14 10,14]", "[9,20 10,20]", "[9,23 9,24]", "[12,8 13,8]", "[12,11 12,12]", "[12,20 13,20]", "[12,26 13,26]", "[15,8 16,8]", "[15,14 16,14]", "[15,17 15,18]", "[15,26 16,26]", "[18,2 19,2]", "[18,5 18,6]", "[18,14 19,14]", "[18,20 19,20]", "[18,23 18,24]", "[21,2 22,2]", "[21,8 22,8]", "[21,11 21,12]", "[21,20 22,20]", "[21,26 22,26]", "[24,8 25,8]", "[24,14 25,14]", "[24,17 24,18]", "[24,26 25,26]", "[27,2 28,2]", "[27,5 27,6]", "[27,14 28,14]", "[27,20 28,20]", "[27,23 27,24]"], "5": ["[2,0 3,0]", "[2,18 3,18]", "[5,6 6,6]", "[5,24 6,24]", "[8,12 9,12]", "[11,0 12,0]", "[11,18 12,18]", "[14,6 15,6]", "[14,24 15,24]", "[17,12 18,12]", "[20,0 21,0]", "[20,18 21,18]", "[23,6 24,6]", "[23,24 24,24]", "[26,12 27,12]"], "8": ["[1,10 2,10]", "[1,28 2,28]", "[7,4 8,4]", "[7,22 8,22]", "[10,10 11,10]", "[10,28 11,28]", "[13,16 14,16]", "[16,22 17,22]", "[19,10 20,10]", "[19,28 20,28]", "[22,16 23,16]", "[25,4 26,4]", "[25,22 26,22]"]}, "input_rect_count": 73}]}, "73251a56": {"task_id": "73251a56", "train_examples": [{"example_id": 0, "input_shape": [21, 21], "output_shape": [21, 21], "input_rectangles": {"0": ["[4,11 5,13]", "[7,8 9,10]", "[18,10 19,13]"], "1": ["[0,2 0,3]", "[0,14 0,15]", "[2,0 3,0]", "[14,0 15,0]"], "2": ["[0,4 0,5]", "[0,16 0,17]", "[1,7 1,9]", "[4,0 5,0]", "[7,1 9,1]", "[16,0 17,0]"], "3": ["[0,6 0,7]", "[0,18 0,19]", "[1,10 1,12]", "[2,14 2,17]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[14,2 17,2]", "[18,0 19,0]", "[18,3 20,3]"], "4": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[8,0 9,0]", "[13,1 15,1]", "[18,2 20,2]"], "5": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "6": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"]}, "output_rectangles": {"1": ["[0,2 0,3]", "[0,14 0,15]", "[2,0 3,0]", "[14,0 15,0]"], "2": ["[0,4 0,5]", "[0,16 0,17]", "[1,7 1,9]", "[4,0 5,0]", "[7,1 9,1]", "[16,0 17,0]"], "3": ["[0,6 0,7]", "[0,18 0,19]", "[1,10 1,12]", "[2,14 2,17]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[14,2 17,2]", "[18,0 19,0]", "[18,3 20,3]"], "4": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[8,0 9,0]", "[13,1 15,1]", "[18,2 20,2]"], "5": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "6": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"]}, "input_rect_count": 37, "output_rect_count": 34}, {"example_id": 1, "input_shape": [21, 21], "output_shape": [21, 21], "input_rectangles": {"0": ["[3,3 7,5]", "[14,2 15,5]", "[17,8 18,11]"], "1": ["[0,6 0,7]", "[1,10 1,12]", "[2,14 2,17]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[16,2 17,2]", "[18,3 20,3]"], "2": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[8,0 9,0]", "[13,1 15,1]", "[18,2 20,2]"], "3": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "4": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"], "5": ["[0,14 0,15]", "[14,0 15,0]"], "6": ["[0,2 0,3]", "[0,16 0,17]", "[2,0 3,0]", "[16,0 17,0]"], "7": ["[0,4 0,5]", "[0,18 0,19]", "[1,7 1,9]", "[4,0 5,0]", "[7,1 9,1]", "[18,0 19,0]"]}, "output_rectangles": {"1": ["[0,6 0,7]", "[1,10 1,12]", "[2,14 2,17]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[14,2 17,2]", "[18,3 20,3]"], "2": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[8,0 9,0]", "[13,1 15,1]", "[18,2 20,2]"], "3": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "4": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"], "5": ["[0,14 0,15]", "[14,0 15,0]"], "6": ["[0,2 0,3]", "[0,16 0,17]", "[2,0 3,0]", "[16,0 17,0]"], "7": ["[0,4 0,5]", "[0,18 0,19]", "[1,7 1,9]", "[4,0 5,0]", "[7,1 9,1]", "[18,0 19,0]"]}, "input_rect_count": 37, "output_rect_count": 34}, {"example_id": 2, "input_shape": [21, 21], "output_shape": [21, 21], "input_rectangles": {"0": ["[2,16 6,17]"], "1": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "2": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"], "3": ["[0,14 0,15]", "[14,0 15,0]"], "4": ["[0,16 0,17]", "[16,0 17,0]"], "5": ["[0,2 0,3]", "[0,18 0,19]", "[2,0 3,0]", "[18,0 19,0]"], "6": ["[0,4 0,5]", "[1,7 1,9]", "[4,0 5,0]", "[7,1 9,1]"], "7": ["[0,6 0,7]", "[1,10 1,12]", "[2,14 2,15]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[14,2 17,2]", "[18,3 20,3]"], "8": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[8,0 9,0]", "[13,1 15,1]", "[18,2 20,2]"]}, "output_rectangles": {"1": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "2": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"], "3": ["[0,14 0,15]", "[14,0 15,0]"], "4": ["[0,16 0,17]", "[16,0 17,0]"], "5": ["[0,2 0,3]", "[0,18 0,19]", "[2,0 3,0]", "[18,0 19,0]"], "6": ["[0,4 0,5]", "[1,7 1,9]", "[4,0 5,0]", "[7,1 9,1]"], "7": ["[0,6 0,7]", "[1,10 1,12]", "[2,14 2,17]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[14,2 17,2]", "[18,3 20,3]"], "8": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[8,0 9,0]", "[13,1 15,1]", "[18,2 20,2]"]}, "input_rect_count": 35, "output_rect_count": 34}], "test_examples": [{"example_id": 0, "input_shape": [21, 21], "input_rectangles": {"0": ["[1,10 5,11]", "[8,0 9,4]", "[15,4 17,8]", "[15,15 16,19]", "[18,12 19,16]"], "1": ["[0,14 0,15]", "[14,0 15,0]"], "2": ["[0,16 0,17]", "[16,0 17,0]"], "3": ["[0,18 0,19]", "[18,0 19,0]"], "4": ["[0,2 0,3]", "[2,0 3,0]"], "5": ["[0,4 0,5]", "[1,7 1,9]", "[4,0 5,0]"], "6": ["[0,6 0,7]", "[2,14 2,17]", "[3,18 3,20]", "[6,0 7,0]", "[10,1 12,1]", "[14,2 17,2]", "[18,3 20,3]"], "7": ["[0,8 0,9]", "[1,13 1,15]", "[2,18 2,20]", "[13,1 15,1]", "[18,2 20,2]"], "8": ["[0,10 0,11]", "[1,16 1,18]", "[10,0 11,0]", "[16,1 18,1]"], "9": ["[0,12 0,13]", "[1,19 1,20]", "[12,0 13,0]", "[19,1 20,1]"]}, "input_rect_count": 36}]}, "780d0b14": {"task_id": "780d0b14", "train_examples": [{"example_id": 0, "input_shape": [20, 20], "output_shape": [2, 2], "input_rectangles": {"0": ["[2,2 3,2]", "[14,5 15,5]", "[14,10 15,10]", "[14,12 15,12]", "[16,16 16,17]", "[18,14 19,14]"]}, "output_rectangles": {}, "input_rect_count": 6, "output_rect_count": 0}, {"example_id": 1, "input_shape": [18, 22], "output_shape": [2, 3], "input_rectangles": {"0": ["[2,9 2,10]", "[3,2 3,3]", "[5,0 5,1]", "[15,1 15,2]"], "4": ["[6,0 6,1]"]}, "output_rectangles": {}, "input_rect_count": 5, "output_rect_count": 0}, {"example_id": 2, "input_shape": [23, 17], "output_shape": [3, 2], "input_rectangles": {"0": ["[0,12 0,13]", "[1,2 1,3]", "[2,4 3,4]", "[9,10 10,10]", "[13,11 13,12]", "[21,13 21,14]"], "7": ["[5,15 5,16]"]}, "output_rectangles": {}, "input_rect_count": 7, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [23, 28], "input_rectangles": {"0": ["[2,2 2,3]", "[9,9 9,10]", "[9,24 10,24]", "[15,10 16,10]"]}, "input_rect_count": 4}]}, "85c4e7cd": {"task_id": "85c4e7cd", "train_examples": [{"example_id": 0, "input_shape": [12, 12], "output_shape": [12, 12], "input_rectangles": {"8": ["[5,5 6,6]"]}, "output_rectangles": {"4": ["[5,5 6,6]"]}, "input_rect_count": 1, "output_rect_count": 1}, {"example_id": 1, "input_shape": [6, 6], "output_shape": [6, 6], "input_rectangles": {"6": ["[2,2 3,3]"]}, "output_rectangles": {"2": ["[2,2 3,3]"]}, "input_rect_count": 1, "output_rect_count": 1}, {"example_id": 2, "input_shape": [8, 8], "output_shape": [8, 8], "input_rectangles": {"4": ["[3,3 4,4]"]}, "output_rectangles": {"8": ["[3,3 4,4]"]}, "input_rect_count": 1, "output_rect_count": 1}, {"example_id": 3, "input_shape": [10, 10], "output_shape": [10, 10], "input_rectangles": {"3": ["[4,4 5,5]"]}, "output_rectangles": {"7": ["[4,4 5,5]"]}, "input_rect_count": 1, "output_rect_count": 1}], "test_examples": [{"example_id": 0, "input_shape": [14, 14], "input_rectangles": {"5": ["[6,6 7,7]"]}, "input_rect_count": 1}]}, "8731374e": {"task_id": "8731374e", "train_examples": [{"example_id": 0, "input_shape": [17, 23], "output_shape": [7, 6], "input_rectangles": {"0": ["[8,19 8,21]"], "1": ["[2,21 3,21]", "[11,0 11,1]", "[12,16 12,17]", "[13,0 13,1]", "[14,4 15,4]"], "2": ["[1,11 3,11]", "[3,0 3,1]", "[7,14 7,15]", "[8,3 9,3]", "[11,6 11,7]", "[12,21 12,22]", "[13,11 13,12]", "[14,2 15,2]", "[15,6 15,7]", "[15,17 15,18]"], "4": ["[8,14 9,14]", "[12,2 13,2]", "[13,13 13,14]", "[15,21 16,21]"], "5": ["[0,15 1,15]", "[2,16 3,16]", "[3,3 3,4]", "[3,18 3,19]", "[14,10 14,11]"], "6": ["[0,0 2,0]", "[3,5 3,6]", "[4,13 4,14]", "[11,20 12,20]", "[15,9 16,9]", "[15,13 15,14]"], "7": ["[0,10 0,11]", "[8,4 9,4]", "[8,15 8,16]", "[9,17 10,17]", "[15,3 16,3]"], "8": ["[1,5 2,5]", "[4,18 4,19]", "[7,21 7,22]", "[12,5 12,6]", "[12,10 12,11]", "[13,15 14,15]", "[13,19 14,19]"], "9": ["[9,21 10,21]", "[10,18 11,18]", "[11,2 11,3]"]}, "output_rectangles": {"1": ["[0,0 1,0]", "[0,2 1,3]", "[0,5 1,5]", "[3,2 3,3]", "[5,0 6,0]", "[5,2 6,3]", "[5,5 6,5]"]}, "input_rect_count": 46, "output_rect_count": 7}, {"example_id": 1, "input_shape": [27, 23], "output_shape": [10, 9], "input_rectangles": {"0": ["[4,15 5,15]", "[7,0 7,1]", "[12,13 12,14]"], "1": ["[4,20 4,21]", "[11,18 11,19]", "[13,17 14,17]", "[18,17 18,18]", "[21,19 22,19]", "[26,13 26,14]"], "2": ["[1,8 2,8]", "[19,18 19,20]", "[20,2 20,3]", "[24,16 25,16]", "[25,0 26,0]", "[25,6 26,6]"], "3": ["[13,15 13,16]", "[21,7 22,7]", "[21,20 21,22]"], "4": ["[0,16 0,17]", "[2,21 2,22]", "[7,17 7,18]", "[9,18 10,18]", "[9,20 10,20]", "[15,15 16,15]", "[17,20 18,20]", "[25,2 25,3]"], "5": ["[4,12 4,13]", "[5,20 5,21]", "[14,13 14,14]"], "6": ["[1,18 1,19]", "[3,12 3,13]", "[7,13 8,13]", "[21,9 21,11]", "[22,12 23,12]", "[23,1 23,2]", "[24,3 24,4]"], "7": ["[7,14 7,15]", "[14,21 15,21]", "[17,17 17,18]", "[18,8 19,8]", "[24,11 25,11]", "[24,22 25,22]"], "8": ["[0,2 1,2]", "[2,15 3,15]", "[4,16 5,16]", "[11,20 11,21]"], "9": ["[1,20 1,21]", "[3,21 3,22]", "[13,21 13,22]", "[14,19 14,20]", "[16,9 17,9]"]}, "output_rectangles": {"4": ["[0,0 1,1]", "[0,3 1,3]", "[0,5 1,5]", "[0,7 1,8]", "[3,0 4,1]", "[3,3 4,3]", "[3,5 4,5]", "[3,7 4,8]", "[6,0 6,1]", "[6,7 6,8]", "[8,0 9,1]", "[8,3 9,3]", "[8,5 9,5]", "[8,7 9,8]"]}, "input_rect_count": 51, "output_rect_count": 14}, {"example_id": 2, "input_shape": [16, 17], "output_shape": [6, 8], "input_rectangles": {"0": ["[2,14 3,14]", "[8,2 8,3]", "[14,8 15,8]"], "1": ["[5,14 5,15]"], "2": ["[2,13 3,13]", "[6,1 7,1]", "[10,0 11,0]", "[10,10 11,10]", "[14,5 14,6]"], "3": ["[1,8 1,9]", "[9,3 9,4]"], "4": ["[4,16 5,16]"], "5": ["[0,13 1,13]", "[3,0 3,1]", "[8,10 8,11]", "[13,10 13,11]", "[14,3 15,3]"], "6": ["[6,16 7,16]"], "7": ["[1,11 1,12]", "[9,15 10,15]"], "8": ["[0,4 0,5]", "[9,12 10,12]", "[11,8 12,8]", "[12,14 12,15]"], "9": ["[0,8 0,9]", "[8,4 8,5]", "[11,12 11,13]", "[13,1 13,3]"]}, "output_rectangles": {"8": ["[0,0 2,2]", "[0,4 2,7]", "[4,0 5,2]", "[4,4 5,7]"]}, "input_rect_count": 28, "output_rect_count": 4}], "test_examples": [{"example_id": 0, "input_shape": [19, 17], "input_rectangles": {"0": ["[15,10 16,10]"], "1": ["[15,1 17,1]"], "2": ["[4,1 5,1]", "[14,1 14,2]"], "4": ["[1,0 1,1]", "[6,1 6,2]", "[9,11 11,11]", "[13,4 14,4]", "[16,3 17,3]"], "5": ["[1,14 2,14]"], "6": ["[7,11 8,11]", "[12,0 12,1]", "[15,6 15,7]"], "7": ["[5,14 6,14]", "[9,1 9,2]", "[11,1 11,2]", "[13,10 13,11]", "[15,15 15,16]"], "8": ["[6,0 7,0]", "[11,12 12,12]", "[15,14 16,14]"], "9": ["[0,9 1,9]"]}, "input_rect_count": 22}]}, "9ecd008a": {"task_id": "9ecd008a", "train_examples": [{"example_id": 0, "input_shape": [16, 16], "output_shape": [3, 3], "input_rectangles": {"0": ["[5,4 7,6]"], "1": ["[7,7 8,8]"], "2": ["[3,7 3,8]", "[7,3 8,3]", "[7,12 8,12]", "[12,7 12,8]"], "3": ["[6,7 6,8]", "[7,9 8,9]", "[9,7 9,8]"], "4": ["[2,2 3,3]", "[2,12 3,13]", "[4,4 4,5]", "[12,2 13,3]", "[12,12 13,13]"], "9": ["[2,7 2,8]", "[5,7 5,8]", "[7,2 8,2]", "[7,10 8,10]", "[7,13 8,13]", "[10,7 10,8]", "[13,7 13,8]"]}, "output_rectangles": {}, "input_rect_count": 21, "output_rect_count": 0}, {"example_id": 1, "input_shape": [16, 16], "output_shape": [3, 3], "input_rectangles": {"0": ["[8,4 10,6]"], "1": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"], "3": ["[4,4 5,5]", "[4,10 5,11]", "[10,10 11,11]", "[11,4 11,5]"], "4": ["[7,7 8,8]"], "7": ["[0,4 1,5]", "[0,10 1,11]", "[2,7 2,8]", "[4,0 5,1]", "[4,14 5,15]", "[7,2 8,2]", "[7,13 8,13]", "[10,0 11,1]", "[10,14 11,15]", "[13,7 13,8]", "[14,4 15,5]", "[14,10 15,11]"], "9": ["[3,7 3,8]", "[7,3 8,3]", "[7,12 8,12]", "[12,7 12,8]"]}, "output_rectangles": {"3": ["[2,0 2,1]"]}, "input_rect_count": 26, "output_rect_count": 1}, {"example_id": 2, "input_shape": [16, 16], "output_shape": [3, 3], "input_rectangles": {"0": ["[7,10 9,12]"], "1": ["[4,7 4,8]", "[7,4 8,4]", "[11,7 11,8]"], "3": ["[0,3 0,4]", "[0,11 0,12]", "[3,0 4,0]", "[3,15 4,15]", "[6,7 6,8]", "[7,6 8,6]", "[7,9 8,9]", "[9,7 9,8]", "[11,0 12,0]", "[11,15 12,15]", "[15,3 15,4]", "[15,11 15,12]"], "6": ["[2,6 3,9]", "[5,7 5,8]", "[6,2 9,3]", "[7,5 8,5]", "[10,7 10,8]", "[12,6 13,9]"], "8": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"], "9": ["[7,7 8,8]"]}, "output_rectangles": {"1": ["[0,1 1,1]"], "6": ["[0,0 1,0]", "[0,2 2,2]"]}, "input_rect_count": 27, "output_rect_count": 3}], "test_examples": [{"example_id": 0, "input_shape": [16, 16], "input_rectangles": {"0": ["[5,1 7,3]"], "1": ["[0,7 0,8]", "[7,0 8,0]", "[7,15 8,15]", "[15,7 15,8]"], "2": ["[4,7 4,8]", "[7,4 8,4]", "[7,11 8,11]", "[11,7 11,8]"], "5": ["[6,7 6,8]", "[7,6 8,6]", "[7,9 8,9]", "[9,7 9,8]"], "7": ["[5,7 5,8]", "[7,5 8,5]", "[7,10 8,10]", "[10,7 10,8]"], "9": ["[0,2 1,3]", "[0,12 1,13]", "[2,0 3,1]", "[2,14 3,15]", "[3,7 3,8]", "[7,7 8,8]", "[7,12 8,12]", "[12,0 13,1]", "[12,7 12,8]", "[12,14 13,15]", "[14,2 15,3]", "[14,12 15,13]"]}, "input_rect_count": 29}]}, "b8825c91": {"task_id": "b8825c91", "train_examples": [{"example_id": 0, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"1": ["[7,7 8,8]"], "3": ["[2,6 3,9]", "[6,2 9,3]", "[8,12 9,13]", "[12,6 13,9]"], "4": ["[5,10 7,13]", "[10,10 13,12]"], "7": ["[4,12 4,13]", "[10,13 11,13]"], "9": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"]}, "output_rectangles": {"1": ["[7,7 8,8]"], "3": ["[2,6 3,9]", "[6,2 9,3]", "[6,12 9,13]", "[12,6 13,9]"], "9": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"]}, "input_rect_count": 13, "output_rect_count": 9}, {"example_id": 1, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"1": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"], "2": ["[3,7 3,8]", "[7,3 8,3]", "[12,7 12,8]"], "4": ["[2,1 4,2]", "[8,11 9,13]"], "8": ["[7,7 8,8]"]}, "output_rectangles": {"1": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"], "2": ["[3,7 3,8]", "[7,3 8,3]", "[7,12 8,12]", "[12,7 12,8]"], "8": ["[7,7 8,8]"]}, "input_rect_count": 10, "output_rect_count": 9}, {"example_id": 2, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"1": ["[3,7 3,8]", "[7,3 8,3]", "[12,7 12,8]"], "3": ["[7,7 8,8]"], "4": ["[6,12 9,13]", "[11,2 14,5]"], "5": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[10,3 10,5]", "[14,7 14,8]"], "6": ["[5,7 5,8]", "[7,5 8,5]", "[7,10 8,10]", "[10,7 10,8]"], "7": ["[4,7 4,8]", "[7,4 8,4]", "[7,11 8,11]", "[11,7 11,8]"], "8": ["[0,7 0,8]", "[7,0 8,0]", "[7,15 8,15]", "[15,7 15,8]"], "9": ["[15,2 15,3]"]}, "output_rectangles": {"1": ["[3,7 3,8]", "[7,3 8,3]", "[7,12 8,12]", "[12,7 12,8]"], "3": ["[7,7 8,8]"], "5": ["[1,7 1,8]", "[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"], "6": ["[5,7 5,8]", "[7,5 8,5]", "[7,10 8,10]", "[10,7 10,8]"], "7": ["[4,7 4,8]", "[7,4 8,4]", "[7,11 8,11]", "[11,7 11,8]"], "8": ["[0,7 0,8]", "[7,0 8,0]", "[7,15 8,15]", "[15,7 15,8]"]}, "input_rect_count": 24, "output_rect_count": 21}, {"example_id": 3, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"2": ["[7,7 8,8]"], "4": ["[1,10 3,13]", "[8,3 10,4]"], "6": ["[2,6 3,9]", "[6,12 9,13]", "[12,6 13,9]"], "7": ["[10,5 11,5]"], "9": ["[0,5 0,6]", "[0,9 0,10]", "[1,4 2,4]", "[4,1 4,2]", "[4,13 4,14]", "[5,0 6,0]", "[5,15 6,15]", "[9,0 10,0]", "[9,15 10,15]", "[11,1 11,2]", "[11,13 11,14]", "[13,4 14,4]", "[13,11 14,11]", "[15,5 15,6]", "[15,9 15,10]"]}, "output_rectangles": {"2": ["[7,7 8,8]"], "6": ["[2,6 3,9]", "[6,2 9,3]", "[6,12 9,13]", "[12,6 13,9]"], "9": ["[0,5 0,6]", "[0,9 0,10]", "[1,4 2,4]", "[1,11 2,11]", "[4,1 4,2]", "[4,13 4,14]", "[5,0 6,0]", "[5,15 6,15]", "[9,0 10,0]", "[9,15 10,15]", "[11,1 11,2]", "[11,13 11,14]", "[13,4 14,4]", "[13,11 14,11]", "[15,5 15,6]", "[15,9 15,10]"]}, "input_rect_count": 22, "output_rect_count": 21}], "test_examples": [{"example_id": 0, "input_shape": [16, 16], "input_rectangles": {"4": ["[2,6 4,8]", "[4,10 7,13]"], "6": ["[4,9 5,9]"], "7": ["[7,7 8,8]"]}, "input_rect_count": 4}]}, "c3f564a4": {"task_id": "c3f564a4", "train_examples": [{"example_id": 0, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"0": ["[1,1 4,3]", "[5,12 6,13]", "[9,10 11,13]"]}, "output_rectangles": {}, "input_rect_count": 3, "output_rect_count": 0}, {"example_id": 1, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"0": ["[9,0 12,3]"]}, "output_rectangles": {}, "input_rect_count": 1, "output_rect_count": 0}, {"example_id": 2, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"0": ["[2,1 5,4]", "[8,1 9,4]"]}, "output_rectangles": {}, "input_rect_count": 2, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [16, 16], "input_rectangles": {"0": ["[0,7 1,9]", "[12,7 13,8]"]}, "input_rect_count": 2}]}, "dc0a314f": {"task_id": "dc0a314f", "train_examples": [{"example_id": 0, "input_shape": [16, 16], "output_shape": [5, 5], "input_rectangles": {"2": ["[0,2 0,3]", "[0,12 0,13]", "[2,0 3,0]", "[2,15 3,15]", "[7,14 8,14]", "[12,0 13,0]", "[12,15 13,15]", "[15,2 15,3]", "[15,12 15,13]"], "3": ["[5,9 9,13]"], "5": ["[4,13 4,14]", "[10,9 11,9]"], "6": ["[0,7 0,8]", "[7,0 8,0]", "[7,15 8,15]", "[15,7 15,8]"], "7": ["[4,7 4,8]", "[7,4 8,4]", "[11,7 11,8]"]}, "output_rectangles": {"5": ["[0,4 1,4]", "[4,0 4,2]"], "7": ["[2,2 3,2]"], "8": ["[0,1 0,2]", "[2,0 3,1]"]}, "input_rect_count": 19, "output_rect_count": 5}, {"example_id": 1, "input_shape": [16, 16], "output_shape": [5, 5], "input_rectangles": {"1": ["[7,3 8,3]", "[7,12 8,12]", "[12,7 12,8]"], "2": ["[3,2 4,2]", "[7,7 8,8]"], "3": ["[0,3 4,7]"], "7": ["[7,2 8,2]", "[7,13 8,13]", "[13,7 13,8]"], "8": ["[6,7 6,8]", "[7,6 8,6]", "[7,9 8,9]", "[9,7 9,8]"]}, "output_rectangles": {}, "input_rect_count": 13, "output_rect_count": 0}, {"example_id": 2, "input_shape": [16, 16], "output_shape": [5, 5], "input_rectangles": {"2": ["[7,3 8,3]", "[7,12 8,12]", "[12,7 12,8]"], "3": ["[0,7 4,11]"], "5": ["[2,6 3,6]", "[7,0 8,0]", "[7,15 8,15]", "[15,7 15,8]"], "6": ["[5,10 5,11]", "[7,7 8,8]"], "9": ["[7,1 8,1]", "[7,14 8,14]", "[14,7 14,8]"]}, "output_rectangles": {"2": ["[3,0 3,1]"], "5": ["[0,0 0,1]"], "9": ["[1,0 1,1]", "[4,0 4,2]"]}, "input_rect_count": 13, "output_rect_count": 4}], "test_examples": [{"example_id": 0, "input_shape": [16, 16], "input_rectangles": {"3": ["[4,8 8,12]"], "5": ["[7,13 8,13]"]}, "input_rect_count": 2}]}, "e26a3af2": {"task_id": "e26a3af2", "train_examples": [{"example_id": 0, "input_shape": [13, 17], "output_shape": [13, 17], "input_rectangles": {"6": ["[5,14 6,14]"]}, "output_rectangles": {"1": ["[0,0 12,4]"], "2": ["[0,12 12,16]"], "3": ["[0,8 12,11]"], "8": ["[0,5 12,7]"]}, "input_rect_count": 1, "output_rect_count": 4}, {"example_id": 1, "input_shape": [13, 14], "output_shape": [13, 14], "input_rectangles": {"5": ["[5,10 6,10]", "[9,8 10,8]"]}, "output_rectangles": {"1": ["[0,11 12,13]"], "2": ["[0,0 12,4]"], "8": ["[0,5 12,10]"]}, "input_rect_count": 2, "output_rect_count": 3}, {"example_id": 2, "input_shape": [15, 14], "output_shape": [15, 14], "input_rectangles": {"1": ["[13,11 14,13]"], "9": ["[13,10 14,10]"]}, "output_rectangles": {"1": ["[13,0 14,13]"], "3": ["[0,0 3,13]"], "7": ["[4,0 9,13]"], "8": ["[10,0 12,13]"]}, "input_rect_count": 2, "output_rect_count": 4}], "test_examples": [{"example_id": 0, "input_shape": [15, 15], "input_rectangles": {"2": ["[5,11 6,11]"], "3": ["[13,0 13,1]", "[14,2 14,3]"], "6": ["[5,5 5,6]"]}, "input_rect_count": 4}]}, "09629e4f": {"task_id": "09629e4f", "train_examples": [{"example_id": 0, "input_shape": [11, 11], "output_shape": [11, 11], "input_rectangles": {"0": ["[0,1 0,2]", "[2,1 2,2]", "[5,0 5,1]", "[5,4 5,5]", "[5,9 5,10]"]}, "output_rectangles": {"0": ["[0,4 2,6]", "[0,8 2,10]", "[4,0 6,2]", "[8,4 10,6]", "[8,8 10,10]"], "2": ["[0,0 2,2]"], "3": ["[4,8 6,10]"], "4": ["[4,4 6,6]"], "6": ["[8,0 10,2]"]}, "input_rect_count": 5, "output_rect_count": 9}, {"example_id": 1, "input_shape": [11, 11], "output_shape": [11, 11], "input_rectangles": {"0": ["[1,4 1,5]", "[1,9 2,9]", "[9,5 9,6]", "[10,1 10,2]"]}, "output_rectangles": {"0": ["[0,0 2,2]", "[0,4 2,6]", "[4,0 6,2]", "[4,8 6,10]", "[8,4 10,6]"], "2": ["[0,8 2,10]"], "3": ["[4,4 6,6]"], "4": ["[8,0 10,2]"], "6": ["[8,8 10,10]"]}, "input_rect_count": 4, "output_rect_count": 9}, {"example_id": 2, "input_shape": [11, 11], "output_shape": [11, 11], "input_rectangles": {"0": ["[1,6 2,6]", "[4,5 5,5]", "[5,8 5,9]", "[8,4 9,4]", "[8,8 8,9]", "[9,0 9,1]", "[10,5 10,6]"]}, "output_rectangles": {"0": ["[0,0 2,2]", "[0,8 2,10]", "[4,0 6,2]", "[8,4 10,6]", "[8,8 10,10]"], "2": ["[8,0 10,2]"], "3": ["[0,4 2,6]"], "4": ["[4,8 6,10]"], "6": ["[4,4 6,6]"]}, "input_rect_count": 7, "output_rect_count": 9}, {"example_id": 3, "input_shape": [11, 11], "output_shape": [11, 11], "input_rectangles": {"0": ["[4,0 6,0]", "[4,8 6,8]"]}, "output_rectangles": {"0": ["[0,0 2,2]", "[0,8 2,10]", "[4,0 6,2]", "[8,0 10,2]", "[8,8 10,10]"], "2": ["[4,4 6,6]"], "3": ["[8,4 10,6]"], "4": ["[0,4 2,6]"], "6": ["[4,8 6,10]"]}, "input_rect_count": 2, "output_rect_count": 9}], "test_examples": [{"example_id": 0, "input_shape": [11, 11], "input_rectangles": {"0": ["[2,5 2,6]", "[4,1 4,2]", "[6,4 6,5]"]}, "input_rect_count": 3}]}, "1c786137": {"task_id": "1c786137", "train_examples": [{"example_id": 0, "input_shape": [23, 21], "output_shape": [6, 8], "input_rectangles": {"0": ["[3,18 3,19]", "[5,1 5,2]", "[5,10 6,10]", "[7,11 7,12]", "[8,2 8,3]", "[8,8 9,8]", "[12,1 12,2]", "[13,7 15,7]", "[13,14 13,16]", "[15,15 15,16]", "[16,5 17,5]", "[16,8 17,8]", "[17,18 17,19]", "[21,3 22,3]"], "1": ["[2,4 2,5]", "[5,12 6,12]", "[6,0 6,1]", "[6,14 7,14]", "[8,11 8,12]", "[10,0 10,2]", "[10,17 11,17]", "[12,12 13,12]", "[15,8 15,9]", "[15,13 16,13]", "[15,19 16,19]", "[18,13 18,14]", "[18,19 18,20]", "[19,0 20,0]", "[19,16 19,17]"], "3": ["[0,17 1,17]", "[1,7 2,7]", "[4,2 4,3]", "[4,18 4,19]", "[6,5 7,6]", "[7,10 8,10]", "[9,1 9,3]", "[9,14 9,15]", "[11,0 11,2]", "[12,3 12,4]", "[13,5 14,5]", "[16,9 18,9]", "[16,12 17,12]", "[16,17 17,17]", "[19,7 19,8]", "[22,15 22,16]"], "5": ["[2,18 2,19]", "[8,5 9,5]"], "8": ["[0,1 0,2]", "[0,5 0,6]", "[0,8 1,8]", "[2,16 2,17]", "[7,2 7,3]", "[8,14 8,15]", "[8,20 9,20]", "[9,11 9,12]", "[10,16 12,16]", "[11,4 11,5]", "[11,10 11,11]", "[15,5 15,6]", "[15,11 16,11]", "[17,10 18,10]", "[19,18 20,18]", "[19,20 22,20]", "[22,12 22,13]"]}, "output_rectangles": {"0": ["[1,5 2,5]", "[3,6 3,7]", "[4,3 5,3]"], "1": ["[1,7 2,7]", "[4,6 4,7]"], "3": ["[2,0 3,1]", "[3,5 4,5]"], "5": ["[4,0 5,0]"], "8": ["[5,6 5,7]"]}, "input_rect_count": 64, "output_rect_count": 9}, {"example_id": 1, "input_shape": [13, 16], "output_shape": [5, 3], "input_rectangles": {"0": ["[2,3 2,4]", "[2,11 2,12]", "[7,1 8,1]", "[10,8 11,8]", "[11,0 11,1]"], "3": ["[7,15 8,15]", "[10,0 10,1]"], "6": ["[0,3 1,4]", "[3,11 3,12]", "[3,14 3,15]", "[4,4 4,5]", "[9,3 9,4]", "[9,9 10,9]"], "9": ["[1,0 1,1]", "[3,3 3,5]", "[3,9 3,10]", "[5,0 5,1]", "[7,0 8,0]", "[7,14 8,14]", "[12,0 12,1]", "[12,7 12,8]"]}, "output_rectangles": {"6": ["[2,0 2,1]"]}, "input_rect_count": 21, "output_rect_count": 1}, {"example_id": 2, "input_shape": [17, 18], "output_shape": [8, 10], "input_rectangles": {"0": ["[0,5 0,6]", "[3,1 4,1]", "[5,8 6,8]", "[5,13 6,13]"], "2": ["[0,0 1,0]", "[6,1 6,2]", "[6,5 7,5]", "[7,10 7,13]", "[8,6 9,6]", "[13,12 14,12]", "[14,14 15,14]", "[14,16 16,16]", "[15,10 15,11]"], "3": ["[0,14 1,14]", "[3,7 3,9]", "[4,4 6,4]", "[4,15 4,17]", "[8,12 8,13]", "[12,5 12,6]", "[14,10 14,11]", "[16,13 16,14]"], "5": ["[0,17 1,17]", "[2,1 2,2]", "[3,16 3,17]", "[4,7 4,10]", "[5,1 5,2]", "[7,17 8,17]", "[12,9 13,9]", "[15,5 16,5]"]}, "output_rectangles": {"0": ["[2,4 3,4]", "[2,9 3,9]"], "2": ["[3,1 4,1]", "[4,6 4,9]", "[5,2 6,2]"], "3": ["[0,3 0,5]", "[1,0 3,0]", "[5,8 5,9]"], "5": ["[1,3 1,6]"]}, "input_rect_count": 29, "output_rect_count": 9}], "test_examples": [{"example_id": 0, "input_shape": [17, 16], "input_rectangles": {"0": ["[0,7 0,8]", "[6,6 7,6]", "[8,4 10,4]"], "1": ["[1,11 1,12]", "[3,10 3,11]", "[5,7 5,9]", "[8,5 9,5]", "[8,15 9,15]", "[15,4 15,5]", "[15,11 16,11]", "[15,13 16,13]"], "8": ["[1,10 2,10]", "[6,4 7,4]", "[10,1 10,2]", "[12,6 12,7]", "[13,0 16,0]", "[14,11 14,13]", "[15,10 16,10]", "[16,14 16,15]"]}, "input_rect_count": 19}]}, "4be741c5": {"task_id": "4be741c5", "train_examples": [{"example_id": 0, "input_shape": [14, 16], "output_shape": [1, 3], "input_rectangles": {}, "output_rectangles": {}, "input_rect_count": 0, "output_rect_count": 0}, {"example_id": 1, "input_shape": [9, 7], "output_shape": [3, 1], "input_rectangles": {}, "output_rectangles": {}, "input_rect_count": 0, "output_rect_count": 0}, {"example_id": 2, "input_shape": [11, 9], "output_shape": [4, 1], "input_rectangles": {}, "output_rectangles": {}, "input_rect_count": 0, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [14, 14], "input_rectangles": {}, "input_rect_count": 0}]}, "662c240a": {"task_id": "662c240a", "train_examples": [{"example_id": 0, "input_shape": [9, 3], "output_shape": [3, 3], "input_rectangles": {"1": ["[3,2 4,2]", "[5,0 5,1]"], "2": ["[3,0 4,1]"]}, "output_rectangles": {}, "input_rect_count": 3, "output_rect_count": 0}, {"example_id": 1, "input_shape": [9, 3], "output_shape": [3, 3], "input_rectangles": {"1": ["[1,1 2,2]"], "2": ["[7,1 8,2]"], "5": ["[0,1 0,2]", "[1,0 2,0]"]}, "output_rectangles": {}, "input_rect_count": 4, "output_rect_count": 0}, {"example_id": 2, "input_shape": [9, 3], "output_shape": [3, 3], "input_rectangles": {"1": ["[6,2 8,2]", "[7,0 8,0]"], "5": ["[4,1 5,2]"], "7": ["[3,1 3,2]", "[4,0 5,0]"]}, "output_rectangles": {"1": ["[0,2 2,2]", "[1,0 2,0]"]}, "input_rect_count": 5, "output_rect_count": 2}, {"example_id": 3, "input_shape": [9, 3], "output_shape": [3, 3], "input_rectangles": {"8": ["[0,0 0,1]"]}, "output_rectangles": {"8": ["[0,0 0,1]"]}, "input_rect_count": 1, "output_rect_count": 1}], "test_examples": [{"example_id": 0, "input_shape": [9, 3], "input_rectangles": {"2": ["[3,2 4,2]", "[5,0 5,1]"], "3": ["[3,0 4,1]"], "4": ["[1,0 2,0]"], "5": ["[1,1 2,1]"], "8": ["[7,1 8,2]"]}, "input_rect_count": 6}]}, "68b16354": {"task_id": "68b16354", "train_examples": [{"example_id": 0, "input_shape": [5, 5], "output_shape": [5, 5], "input_rectangles": {"2": ["[0,2 2,2]"], "4": ["[1,0 1,1]", "[1,3 2,3]"], "8": ["[1,4 2,4]"]}, "output_rectangles": {"2": ["[2,2 4,2]"], "4": ["[2,3 3,3]", "[3,0 3,1]"], "8": ["[2,4 3,4]"]}, "input_rect_count": 4, "output_rect_count": 4}, {"example_id": 1, "input_shape": [5, 5], "output_shape": [5, 5], "input_rectangles": {"3": ["[0,1 0,2]"], "7": ["[4,2 4,3]"]}, "output_rectangles": {"3": ["[4,1 4,2]"], "7": ["[0,2 0,3]"]}, "input_rect_count": 2, "output_rect_count": 2}, {"example_id": 2, "input_shape": [7, 7], "output_shape": [7, 7], "input_rectangles": {"1": ["[3,0 3,1]"], "2": ["[0,0 1,0]"]}, "output_rectangles": {"1": ["[3,0 3,1]"], "2": ["[5,0 6,0]"]}, "input_rect_count": 2, "output_rect_count": 2}], "test_examples": [{"example_id": 0, "input_shape": [7, 7], "input_rectangles": {"3": ["[2,6 3,6]"], "4": ["[1,0 1,1]", "[1,4 2,4]", "[4,6 6,6]"], "8": ["[4,5 5,5]"]}, "input_rect_count": 5}]}, "855e0971": {"task_id": "855e0971", "train_examples": [{"example_id": 0, "input_shape": [15, 19], "output_shape": [15, 19], "input_rectangles": {"5": ["[0,0 1,18]"]}, "output_rectangles": {"0": ["[2,4 8,4]", "[2,13 8,13]", "[9,9 14,9]"], "4": ["[2,0 8,3]", "[2,5 8,12]", "[2,14 8,18]"], "5": ["[0,0 1,18]"], "8": ["[9,0 14,8]", "[9,10 14,18]"]}, "input_rect_count": 1, "output_rect_count": 9}, {"example_id": 1, "input_shape": [14, 13], "output_shape": [14, 13], "input_rectangles": {}, "output_rectangles": {"0": ["[3,0 3,4]", "[11,5 11,12]"], "1": ["[0,5 10,12]", "[12,5 13,12]"], "2": ["[0,0 2,4]", "[4,0 13,4]"]}, "input_rect_count": 0, "output_rect_count": 6}, {"example_id": 2, "input_shape": [13, 15], "output_shape": [13, 15], "input_rectangles": {}, "output_rectangles": {"0": ["[0,3 4,3]", "[0,11 4,11]", "[5,5 9,5]", "[10,7 12,7]"], "2": ["[5,0 9,4]", "[5,6 9,14]"], "3": ["[10,0 12,6]", "[10,8 12,14]"], "8": ["[0,0 4,2]", "[0,4 4,10]", "[0,12 4,14]"]}, "input_rect_count": 0, "output_rect_count": 11}, {"example_id": 3, "input_shape": [14, 15], "output_shape": [14, 15], "input_rectangles": {"2": ["[0,0 13,3]"]}, "output_rectangles": {"0": ["[2,9 2,14]", "[6,4 6,8]", "[12,9 12,14]"], "2": ["[0,0 13,3]"], "4": ["[0,9 1,14]", "[3,9 11,14]", "[13,9 13,14]"], "5": ["[0,4 5,8]", "[7,4 13,8]"]}, "input_rect_count": 1, "output_rect_count": 9}], "test_examples": [{"example_id": 0, "input_shape": [17, 15], "input_rectangles": {}, "input_rect_count": 0}]}, "c909285e": {"task_id": "c909285e", "train_examples": [{"example_id": 0, "input_shape": [24, 24], "output_shape": [7, 7], "input_rectangles": {"0": ["[0,0 1,1]", "[0,6 1,6]", "[0,10 1,10]", "[0,12 1,13]", "[0,16 1,16]", "[0,18 1,18]", "[0,21 1,22]", "[6,0 6,1]", "[6,12 6,13]", "[6,21 6,22]", "[10,0 10,1]", "[10,12 10,13]", "[10,21 10,22]", "[12,0 13,1]", "[12,6 13,6]", "[12,10 13,10]", "[12,12 13,13]", "[12,16 13,16]", "[12,18 13,18]", "[12,21 13,22]", "[16,0 16,1]", "[16,12 16,13]", "[16,21 16,22]", "[18,0 18,1]", "[18,12 18,13]", "[18,21 18,22]", "[21,0 22,1]", "[21,6 22,6]", "[21,10 22,10]", "[21,12 22,13]", "[21,16 22,16]", "[21,18 22,18]", "[21,21 22,22]"], "5": ["[0,5 4,5]", "[0,11 4,11]", "[5,0 5,4]", "[11,0 11,4]"], "8": ["[0,4 1,4]", "[0,9 1,9]", "[4,0 4,1]", "[4,9 4,10]", "[4,12 4,13]", "[4,21 4,22]", "[9,0 9,1]", "[9,4 10,4]", "[9,12 9,13]", "[9,21 9,22]", "[12,4 13,4]", "[12,9 13,9]", "[21,4 22,4]", "[21,9 22,9]"]}, "output_rectangles": {}, "input_rect_count": 51, "output_rect_count": 0}, {"example_id": 1, "input_shape": [26, 26], "output_shape": [7, 7], "input_rectangles": {"0": ["[0,0 1,1]", "[0,6 1,6]", "[0,10 1,10]", "[0,12 1,13]", "[0,16 1,16]", "[0,18 1,18]", "[0,21 1,22]", "[0,25 1,25]", "[6,0 6,1]", "[6,12 6,13]", "[6,21 6,22]", "[10,0 10,1]", "[10,12 10,13]", "[10,21 10,22]", "[12,0 13,1]", "[12,6 13,6]", "[12,10 13,10]", "[12,12 13,13]", "[12,16 13,16]", "[12,18 13,18]", "[12,21 13,22]", "[12,25 13,25]", "[16,0 16,1]", "[16,12 16,13]", "[16,21 16,22]", "[18,0 18,1]", "[18,12 18,13]", "[18,21 18,22]", "[21,0 22,1]", "[21,6 22,6]", "[21,10 22,10]", "[21,12 22,13]", "[21,16 22,16]", "[21,18 22,18]", "[21,21 22,22]", "[21,25 22,25]", "[25,0 25,1]", "[25,12 25,13]", "[25,21 25,22]"], "1": ["[0,4 1,4]", "[0,9 1,9]", "[0,24 1,24]", "[4,0 4,1]", "[4,9 4,10]", "[4,12 4,13]", "[4,21 4,22]", "[4,24 4,25]", "[9,0 9,1]", "[9,4 10,4]", "[9,12 9,13]", "[9,21 9,22]", "[12,4 13,4]", "[12,9 13,9]", "[12,24 13,24]", "[21,4 22,4]", "[21,9 22,9]", "[21,24 22,24]", "[24,0 24,1]", "[24,4 25,4]", "[24,12 24,13]", "[24,21 24,22]"], "3": ["[0,3 1,3]", "[0,7 1,7]", "[0,15 1,15]", "[0,19 1,19]", "[3,0 3,1]", "[3,9 3,10]", "[3,12 3,13]", "[3,21 3,22]", "[3,24 3,25]", "[7,0 7,1]", "[7,9 7,10]", "[7,12 7,13]", "[7,21 7,22]", "[7,24 7,25]", "[9,3 10,3]", "[9,7 10,7]", "[9,15 10,15]", "[9,19 10,19]", "[12,3 13,3]", "[12,7 13,7]", "[12,15 13,15]", "[12,19 13,19]", "[15,0 15,1]", "[15,9 15,10]", "[15,12 15,13]", "[15,21 15,22]", "[15,24 15,25]", "[19,0 19,1]", "[19,9 19,10]", "[19,12 19,13]", "[19,21 19,22]", "[19,24 19,25]", "[21,3 22,3]", "[21,7 22,7]", "[21,15 22,15]", "[21,19 22,19]", "[24,3 25,3]", "[24,7 25,7]", "[24,15 25,15]", "[24,19 25,19]"], "8": ["[0,2 1,2]", "[0,5 1,5]", "[0,8 1,8]", "[2,0 2,1]", "[5,0 5,1]", "[8,0 8,1]"]}, "output_rectangles": {}, "input_rect_count": 107, "output_rect_count": 0}, {"example_id": 2, "input_shape": [28, 28], "output_shape": [7, 7], "input_rectangles": {"0": ["[0,0 1,1]", "[0,6 1,6]", "[0,10 1,10]", "[0,12 1,13]", "[0,16 1,16]", "[0,18 1,18]", "[0,21 1,22]", "[0,25 1,25]", "[6,0 6,1]", "[6,12 6,13]", "[6,21 6,22]", "[10,0 10,1]", "[10,12 10,13]", "[10,21 10,22]", "[12,0 13,1]", "[12,6 13,6]", "[12,10 13,10]", "[12,12 13,13]", "[12,16 13,16]", "[12,18 13,18]", "[12,21 13,22]", "[12,25 13,25]", "[16,0 16,1]", "[16,12 16,13]", "[16,21 16,22]", "[18,0 18,1]", "[18,12 18,13]", "[18,21 18,22]", "[21,0 22,1]", "[21,6 22,6]", "[21,10 22,10]", "[21,12 22,13]", "[21,16 22,16]", "[21,18 22,18]", "[21,21 22,22]", "[21,25 22,25]", "[25,0 25,1]", "[25,12 25,13]", "[25,21 25,22]"], "1": ["[0,3 1,3]", "[0,7 1,7]", "[0,15 1,15]", "[0,27 1,27]", "[3,0 3,1]", "[3,6 3,7]", "[3,12 3,13]", "[3,15 3,16]", "[3,21 3,22]", "[6,3 7,3]", "[6,27 7,27]", "[7,0 7,1]", "[7,12 7,13]", "[7,21 7,22]", "[12,3 13,3]", "[12,7 13,7]", "[12,15 13,15]", "[12,27 13,27]", "[15,0 15,1]", "[15,3 16,3]", "[15,12 15,13]", "[15,21 15,22]", "[15,27 16,27]", "[21,3 22,3]", "[21,7 22,7]", "[21,15 22,15]", "[21,27 22,27]", "[27,0 27,1]", "[27,6 27,7]", "[27,12 27,13]", "[27,15 27,16]", "[27,21 27,22]"], "5": ["[17,0 17,4]", "[23,0 23,4]", "[24,5 27,5]", "[24,11 27,11]"], "8": ["[0,4 1,4]", "[0,9 1,9]", "[0,19 1,19]", "[0,24 1,24]", "[4,0 4,1]", "[4,6 4,7]", "[4,12 4,13]", "[4,15 4,16]", "[4,21 4,22]", "[6,4 7,4]", "[6,9 7,9]", "[6,19 7,19]", "[6,24 7,24]", "[9,0 9,1]", "[9,6 9,7]", "[9,12 9,13]", "[9,15 9,16]", "[9,21 9,22]", "[12,4 13,4]", "[12,9 13,9]", "[12,19 13,19]", "[12,24 13,24]", "[15,4 16,4]", "[15,9 16,9]", "[15,19 16,19]", "[15,24 16,24]", "[19,0 19,1]", "[19,6 19,7]", "[19,12 19,13]", "[19,15 19,16]", "[19,21 19,22]", "[21,4 22,4]", "[21,9 22,9]", "[21,19 22,19]", "[21,24 22,24]", "[24,0 24,1]", "[24,6 24,7]", "[24,12 24,13]", "[24,15 24,16]", "[24,21 24,22]"]}, "output_rectangles": {"0": ["[4,1 5,1]", "[4,5 5,5]"], "1": ["[4,2 5,2]"], "8": ["[2,1 2,2]", "[4,4 5,4]"]}, "input_rect_count": 115, "output_rect_count": 5}], "test_examples": [{"example_id": 0, "input_shape": [24, 24], "input_rectangles": {"0": ["[0,0 1,1]", "[0,6 1,6]", "[0,10 1,10]", "[0,12 1,13]", "[0,16 1,16]", "[0,18 1,18]", "[0,21 1,22]", "[6,0 6,1]", "[6,12 6,13]", "[6,21 6,22]", "[10,0 10,1]", "[10,12 10,13]", "[10,21 10,22]", "[12,0 13,1]", "[12,6 13,6]", "[12,10 13,10]", "[12,12 13,13]", "[12,16 13,16]", "[12,18 13,18]", "[12,21 13,22]", "[16,0 16,1]", "[16,12 16,13]", "[16,21 16,22]", "[18,0 18,1]", "[18,12 18,13]", "[18,21 18,22]", "[21,0 22,1]", "[21,6 22,6]", "[21,10 22,10]", "[21,12 22,13]", "[21,16 22,16]", "[21,18 22,18]", "[21,21 22,22]"], "1": ["[0,8 2,8]", "[2,12 2,13]", "[8,0 8,2]", "[8,12 8,13]", "[8,20 8,22]", "[12,2 13,2]", "[12,8 13,8]", "[12,20 13,20]", "[20,8 22,8]", "[20,12 20,13]"], "2": ["[3,12 3,13]", "[3,20 3,22]", "[7,12 7,13]", "[7,20 7,22]", "[12,3 13,3]", "[12,7 13,7]", "[12,15 13,15]", "[15,12 15,13]", "[15,20 15,22]", "[20,3 22,3]", "[20,7 22,7]", "[20,15 22,15]"], "3": ["[12,14 13,14]", "[12,19 13,19]", "[14,12 14,13]", "[14,20 14,22]", "[19,12 19,13]", "[19,20 19,22]", "[20,14 22,14]", "[20,19 22,19]"]}, "input_rect_count": 63}]}, "d511f180": {"task_id": "d511f180", "train_examples": [{"example_id": 0, "input_shape": [5, 5], "output_shape": [5, 5], "input_rectangles": {"8": ["[0,2 0,4]"]}, "output_rectangles": {"5": ["[0,2 0,4]"]}, "input_rect_count": 1, "output_rect_count": 1}, {"example_id": 1, "input_shape": [3, 3], "output_shape": [3, 3], "input_rectangles": {"5": ["[0,1 1,1]"]}, "output_rectangles": {"8": ["[0,1 1,1]"]}, "input_rect_count": 1, "output_rect_count": 1}, {"example_id": 2, "input_shape": [3, 3], "output_shape": [3, 3], "input_rectangles": {"8": ["[2,0 2,1]"]}, "output_rectangles": {"5": ["[2,0 2,1]"]}, "input_rect_count": 1, "output_rect_count": 1}], "test_examples": [{"example_id": 0, "input_shape": [4, 4], "input_rectangles": {"3": ["[1,0 2,0]"], "5": ["[0,3 1,3]"], "8": ["[3,2 3,3]"]}, "input_rect_count": 3}]}, "de1cd16c": {"task_id": "de1cd16c", "train_examples": [{"example_id": 0, "input_shape": [17, 13], "output_shape": [1, 1], "input_rectangles": {}, "output_rectangles": {}, "input_rect_count": 0, "output_rect_count": 0}, {"example_id": 1, "input_shape": [16, 15], "output_shape": [1, 1], "input_rectangles": {}, "output_rectangles": {}, "input_rect_count": 0, "output_rect_count": 0}, {"example_id": 2, "input_shape": [16, 17], "output_shape": [1, 1], "input_rectangles": {"0": ["[8,0 15,6]"]}, "output_rectangles": {}, "input_rect_count": 1, "output_rect_count": 0}, {"example_id": 3, "input_shape": [16, 19], "output_shape": [1, 1], "input_rectangles": {"1": ["[0,0 15,6]"]}, "output_rectangles": {}, "input_rect_count": 1, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [18, 19], "input_rectangles": {}, "input_rect_count": 0}]}, "3631a71a": {"task_id": "3631a71a", "train_examples": [{"example_id": 0, "input_shape": [30, 30], "output_shape": [30, 30], "input_rectangles": {"0": ["[15,11 16,11]", "[15,14 16,14]", "[15,17 16,17]", "[15,20 16,20]", "[17,15 17,16]", "[20,15 20,16]"], "1": ["[15,15 16,16]"], "2": ["[3,15 3,16]", "[15,3 16,3]", "[15,28 16,28]", "[28,15 28,16]"], "6": ["[0,8 1,9]", "[0,22 1,23]", "[22,0 23,1]"], "7": ["[1,15 1,16]", "[2,2 3,3]", "[2,28 3,29]", "[15,1 16,1]", "[15,10 16,10]", "[15,21 16,21]", "[21,15 21,16]", "[28,2 29,3]", "[28,28 29,29]"], "9": ["[8,0 10,6]", "[9,12 14,18]"]}, "output_rectangles": {"0": ["[11,15 11,16]", "[14,15 14,16]", "[15,11 16,11]", "[15,14 16,14]", "[15,17 16,17]", "[15,20 16,20]", "[17,15 17,16]", "[20,15 20,16]"], "1": ["[15,15 16,16]"], "2": ["[3,15 3,16]", "[15,3 16,3]", "[15,28 16,28]", "[28,15 28,16]"], "6": ["[0,8 1,9]", "[0,22 1,23]", "[8,0 9,1]", "[22,0 23,1]"], "7": ["[1,15 1,16]", "[2,2 3,3]", "[2,28 3,29]", "[10,15 10,16]", "[15,1 16,1]", "[15,10 16,10]", "[15,21 16,21]", "[21,15 21,16]", "[28,2 29,3]", "[28,28 29,29]"]}, "input_rect_count": 25, "output_rect_count": 27}, {"example_id": 1, "input_shape": [30, 30], "output_shape": [30, 30], "input_rectangles": {"0": ["[1,11 1,12]", "[1,19 1,20]", "[3,9 4,9]", "[7,15 8,16]", "[9,3 9,4]", "[9,13 10,14]", "[9,17 10,18]", "[9,27 9,28]", "[11,1 12,1]", "[11,11 12,12]", "[11,19 12,20]", "[13,9 14,10]", "[13,21 14,22]", "[15,7 16,8]", "[15,23 16,24]", "[17,21 17,22]", "[18,9 18,10]", "[19,1 20,1]", "[19,11 20,12]", "[19,19 20,20]", "[21,13 22,14]", "[21,17 22,18]", "[22,3 22,4]", "[22,27 22,28]", "[23,15 24,16]", "[27,9 28,9]"], "1": ["[5,24 5,25]", "[27,23 27,25]"], "2": ["[21,20 21,21]", "[24,24 24,25]"], "3": ["[0,20 0,21]"], "6": ["[2,14 3,17]", "[6,10 7,11]", "[6,20 7,21]", "[10,6 11,7]", "[10,24 11,25]", "[14,2 17,3]", "[14,28 17,29]", "[20,6 21,7]", "[20,24 21,25]", "[24,10 25,11]", "[24,20 24,21]", "[28,14 29,16]"], "7": ["[9,15 10,16]", "[11,13 11,14]", "[11,17 11,18]", "[13,11 14,11]", "[13,20 14,20]", "[15,9 15,10]", "[15,21 16,22]", "[17,11 18,11]", "[17,20 18,20]", "[20,13 20,14]", "[20,17 20,18]", "[21,15 22,16]"], "8": ["[15,15 16,16]"], "9": ["[1,21 4,25]", "[16,9 17,10]", "[18,21 20,22]", "[25,20 26,25]", "[28,17 29,23]"]}, "output_rectangles": {"0": ["[1,11 1,12]", "[1,19 1,20]", "[3,9 4,9]", "[3,22 4,22]", "[7,15 8,16]", "[9,3 9,4]", "[9,13 10,14]", "[9,17 10,18]", "[9,27 9,28]", "[11,1 12,1]", "[11,11 12,12]", "[11,19 12,20]", "[13,9 14,10]", "[13,21 14,22]", "[15,7 16,8]", "[15,23 16,24]", "[17,9 18,10]", "[17,21 18,22]", "[19,1 20,1]", "[19,11 20,12]", "[19,19 20,20]", "[21,13 22,14]", "[21,17 22,18]", "[22,3 22,4]", "[22,27 22,28]", "[23,15 24,16]", "[27,9 28,9]", "[27,22 28,22]"], "6": ["[2,14 3,17]", "[6,10 7,11]", "[6,20 7,21]", "[10,6 11,7]", "[10,24 11,25]", "[14,2 17,3]", "[14,28 17,29]", "[20,6 21,7]", "[20,24 21,25]", "[24,10 25,11]", "[24,20 25,21]", "[28,14 29,17]"], "7": ["[9,15 10,16]", "[11,13 11,14]", "[11,17 11,18]", "[13,11 14,11]", "[13,20 14,20]", "[15,9 16,10]", "[15,21 16,22]", "[17,11 18,11]", "[17,20 18,20]", "[20,13 20,14]", "[20,17 20,18]", "[21,15 22,16]"], "8": ["[15,15 16,16]"]}, "input_rect_count": 61, "output_rect_count": 53}, {"example_id": 2, "input_shape": [30, 30], "output_shape": [30, 30], "input_rectangles": {"0": ["[3,15 3,16]", "[3,21 5,21]", "[7,15 8,16]", "[11,15 12,16]", "[14,15 14,16]", "[15,3 16,3]", "[15,7 16,8]", "[15,11 16,12]", "[15,14 16,14]", "[15,17 16,17]", "[15,19 16,20]", "[15,23 16,24]", "[15,28 16,28]", "[17,15 17,16]", "[19,15 20,16]", "[23,15 24,16]", "[28,15 28,16]"], "2": ["[15,15 16,16]"], "3": ["[1,15 1,16]", "[15,1 16,1]"], "4": ["[6,2 6,3]", "[8,3 9,5]"], "5": ["[6,15 6,16]", "[7,14 8,14]", "[7,17 8,17]", "[9,15 9,16]", "[14,7 14,8]", "[14,23 14,24]", "[15,6 16,6]", "[15,9 16,9]", "[15,22 16,22]", "[15,25 16,25]", "[17,7 17,8]", "[17,23 17,24]", "[22,15 22,16]", "[23,14 24,14]", "[23,17 24,17]", "[25,15 25,16]"], "6": ["[10,15 10,16]", "[15,10 16,10]", "[15,21 16,21]", "[21,15 21,16]"], "7": ["[7,18 7,19]", "[13,15 13,16]", "[15,13 16,13]", "[15,18 16,18]", "[18,15 18,16]"], "8": ["[4,12 5,13]", "[6,21 7,21]", "[12,4 13,5]", "[12,26 13,27]", "[18,4 19,5]", "[18,26 19,27]", "[26,12 27,13]", "[26,18 27,19]"], "9": ["[0,17 6,20]", "[7,0 13,2]"]}, "output_rectangles": {"0": ["[3,15 3,16]", "[7,15 8,16]", "[11,15 12,16]", "[14,15 14,16]", "[15,3 16,3]", "[15,7 16,8]", "[15,11 16,12]", "[15,14 16,14]", "[15,17 16,17]", "[15,19 16,20]", "[15,23 16,24]", "[15,28 16,28]", "[17,15 17,16]", "[19,15 20,16]", "[23,15 24,16]", "[28,15 28,16]"], "2": ["[15,15 16,16]"], "3": ["[1,15 1,16]", "[15,1 16,1]"], "5": ["[6,15 6,16]", "[7,14 8,14]", "[7,17 8,17]", "[9,15 9,16]", "[14,7 14,8]", "[14,23 14,24]", "[15,6 16,6]", "[15,9 16,9]", "[15,22 16,22]", "[15,25 16,25]", "[17,7 17,8]", "[17,23 17,24]", "[22,15 22,16]", "[23,14 24,14]", "[23,17 24,17]", "[25,15 25,16]"], "6": ["[10,15 10,16]", "[15,10 16,10]", "[15,21 16,21]", "[21,15 21,16]"], "7": ["[13,15 13,16]", "[15,13 16,13]", "[15,18 16,18]", "[18,15 18,16]"], "8": ["[4,12 5,13]", "[4,18 5,19]", "[12,4 13,5]", "[12,26 13,27]", "[18,4 19,5]", "[18,26 19,27]", "[26,12 27,13]", "[26,18 27,19]"]}, "input_rect_count": 57, "output_rect_count": 51}, {"example_id": 3, "input_shape": [30, 30], "output_shape": [30, 30], "input_rectangles": {"0": ["[1,15 1,16]", "[10,10 10,12]", "[11,15 11,16]", "[15,1 16,1]", "[15,3 16,4]", "[15,11 16,11]", "[15,15 16,16]", "[15,20 16,20]", "[20,15 20,16]"], "1": ["[8,10 9,11]", "[8,20 9,21]", "[10,8 10,9]", "[10,22 11,23]", "[20,8 21,9]", "[20,22 21,23]", "[22,20 23,21]"], "2": ["[0,15 0,16]", "[10,13 12,13]", "[15,0 16,0]"], "3": ["[2,10 3,11]", "[2,20 3,21]", "[4,8 5,8]", "[10,2 11,3]", "[11,28 11,29]", "[20,2 21,3]", "[20,28 21,29]", "[26,8 27,8]", "[28,11 29,11]", "[28,20 29,21]"], "4": ["[4,12 5,13]", "[4,18 5,19]", "[7,15 7,16]", "[12,4 13,4]", "[12,26 13,27]", "[15,24 16,24]", "[18,4 19,5]", "[18,26 19,27]", "[24,15 24,16]", "[26,12 27,13]", "[26,18 27,19]"], "7": ["[10,6 10,7]"], "8": ["[22,8 22,9]"], "9": ["[4,9 6,11]", "[7,27 10,29]", "[23,9 29,10]"]}, "output_rectangles": {"0": ["[1,15 1,16]", "[11,15 11,16]", "[15,1 16,1]", "[15,11 16,11]", "[15,15 16,16]", "[15,20 16,20]", "[20,15 20,16]"], "1": ["[8,10 9,11]", "[8,20 9,21]", "[10,8 11,9]", "[10,22 11,23]", "[20,8 21,9]", "[20,22 21,23]", "[22,10 23,11]", "[22,20 23,21]"], "2": ["[0,15 0,16]", "[15,0 16,0]"], "3": ["[2,10 3,11]", "[2,20 3,21]", "[10,2 11,3]", "[10,28 11,29]", "[20,2 21,3]", "[20,28 21,29]", "[28,10 29,11]", "[28,20 29,21]"], "4": ["[4,12 5,13]", "[4,18 5,19]", "[7,15 7,16]", "[12,4 13,5]", "[12,26 13,27]", "[15,7 16,7]", "[15,24 16,24]", "[18,4 19,5]", "[18,26 19,27]", "[24,15 24,16]", "[26,12 27,13]", "[26,18 27,19]"]}, "input_rect_count": 45, "output_rect_count": 37}], "test_examples": [{"example_id": 0, "input_shape": [30, 30], "input_rectangles": {"0": ["[5,4 6,4]", "[7,15 8,16]", "[10,27 11,27]", "[15,7 16,8]", "[15,23 16,24]", "[23,15 24,16]", "[29,17 29,19]"], "1": ["[0,6 1,7]", "[0,24 1,25]", "[2,26 3,27]", "[4,28 5,29]", "[5,2 5,3]", "[6,0 7,1]", "[24,0 25,1]", "[26,2 27,3]", "[26,28 27,29]", "[28,4 29,5]", "[28,26 29,27]"], "2": ["[15,15 16,16]"], "3": ["[9,15 9,16]", "[15,9 16,9]", "[15,22 16,22]", "[22,15 22,16]"], "6": ["[2,15 2,16]", "[4,12 5,13]", "[4,18 5,19]", "[12,4 13,5]", "[12,26 13,27]", "[15,2 16,2]", "[15,29 16,29]", "[18,4 19,5]", "[18,26 19,27]", "[26,12 27,13]", "[26,18 26,19]", "[26,24 26,25]", "[29,15 29,16]"], "7": ["[0,4 1,5]", "[0,26 1,27]", "[2,10 3,11]", "[2,20 2,21]", "[5,0 5,1]", "[10,2 11,3]", "[10,28 11,29]", "[20,2 21,3]", "[20,28 21,29]", "[26,0 27,1]", "[28,10 29,11]", "[29,20 29,21]"], "8": ["[26,22 26,23]"], "9": ["[2,0 4,6]", "[27,18 28,24]"]}, "input_rect_count": 51}]}, "8e1813be": {"task_id": "8e1813be", "train_examples": [{"example_id": 0, "input_shape": [18, 15], "output_shape": [6, 6], "input_rectangles": {"0": ["[0,0 1,14]", "[3,0 4,14]", "[6,0 7,14]"], "1": ["[5,0 5,14]"], "2": ["[2,0 2,14]"], "3": ["[8,0 8,14]"], "4": ["[11,8 11,14]"], "5": ["[10,1 15,6]"], "6": ["[17,0 17,14]"], "8": ["[14,8 14,14]"]}, "output_rectangles": {"1": ["[1,0 1,5]"], "2": ["[0,0 0,5]"], "3": ["[2,0 2,5]"], "4": ["[3,0 3,5]"], "6": ["[5,0 5,5]"], "8": ["[4,0 4,5]"]}, "input_rect_count": 10, "output_rect_count": 6}, {"example_id": 1, "input_shape": [12, 10], "output_shape": [3, 3], "input_rectangles": {"0": ["[0,6 11,7]", "[0,9 11,9]"], "1": ["[5,2 11,2]"], "2": ["[0,5 11,5]"], "4": ["[0,8 11,8]"], "5": ["[1,1 3,3]"]}, "output_rectangles": {"1": ["[0,0 2,0]"], "2": ["[0,1 2,1]"], "4": ["[0,2 2,2]"]}, "input_rect_count": 6, "output_rect_count": 3}, {"example_id": 2, "input_shape": [12, 12], "output_shape": [4, 4], "input_rectangles": {"0": ["[8,0 9,11]", "[11,0 11,11]"], "1": ["[10,0 10,11]"], "2": ["[1,0 1,4]"], "4": ["[7,0 7,11]"], "5": ["[1,6 4,9]"], "8": ["[4,0 4,4]"]}, "output_rectangles": {"1": ["[3,0 3,3]"], "2": ["[0,0 0,3]"], "4": ["[2,0 2,3]"], "8": ["[1,0 1,3]"]}, "input_rect_count": 7, "output_rect_count": 4}], "test_examples": [{"example_id": 0, "input_shape": [19, 19], "input_rectangles": {"0": ["[10,0 11,18]", "[13,0 14,18]", "[16,0 17,18]"], "1": ["[15,0 15,18]"], "2": ["[0,0 0,6]", "[0,16 0,18]"], "3": ["[3,0 3,6]", "[3,16 3,18]"], "4": ["[9,0 9,18]"], "5": ["[1,8 7,14]"], "6": ["[12,0 12,18]"], "7": ["[18,0 18,18]"], "8": ["[6,0 6,6]", "[6,16 6,18]"]}, "input_rect_count": 14}]}, "90c28cc7": {"task_id": "90c28cc7", "train_examples": [{"example_id": 0, "input_shape": [21, 21], "output_shape": [3, 3], "input_rectangles": {"1": ["[7,15 10,20]"], "2": ["[11,1 15,8]"], "3": ["[7,1 10,8]"], "4": ["[7,9 10,14]"], "5": ["[11,9 15,20]"], "7": ["[1,9 6,20]"], "8": ["[1,1 6,8]"]}, "output_rectangles": {"5": ["[2,1 2,2]"], "7": ["[0,1 0,2]"]}, "input_rect_count": 7, "output_rect_count": 2}, {"example_id": 1, "input_shape": [21, 21], "output_shape": [2, 2], "input_rectangles": {"1": ["[9,1 15,7]"], "2": ["[1,1 8,7]"], "4": ["[9,8 15,15]"], "8": ["[1,8 8,15]"]}, "output_rectangles": {}, "input_rect_count": 4, "output_rect_count": 0}, {"example_id": 2, "input_shape": [21, 21], "output_shape": [3, 2], "input_rectangles": {"1": ["[13,8 18,13]"], "2": ["[2,8 6,13]"], "3": ["[7,2 12,13]"], "4": ["[13,2 18,7]"], "8": ["[2,2 6,7]"]}, "output_rectangles": {"3": ["[1,0 1,1]"]}, "input_rect_count": 5, "output_rect_count": 1}], "test_examples": [{"example_id": 0, "input_shape": [21, 21], "input_rectangles": {"1": ["[1,14 6,18]"], "2": ["[1,1 6,5]", "[15,1 18,5]", "[15,14 18,18]"], "3": ["[7,6 14,13]"], "4": ["[1,6 6,13]", "[15,6 18,13]"], "8": ["[7,1 14,5]", "[7,14 14,18]"]}, "input_rect_count": 9}]}, "91714a58": {"task_id": "91714a58", "train_examples": [{"example_id": 0, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"0": ["[6,1 7,1]", "[10,14 10,15]", "[15,12 15,13]"], "1": ["[0,4 0,5]"], "3": ["[6,9 7,9]"], "7": ["[13,4 14,4]"], "9": ["[1,8 1,9]"]}, "output_rectangles": {"2": ["[3,3 6,5]"]}, "input_rect_count": 7, "output_rect_count": 1}, {"example_id": 1, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"0": ["[4,3 4,4]", "[8,10 9,10]"], "2": ["[3,0 3,1]"], "4": ["[5,0 5,1]", "[12,11 12,12]"], "6": ["[11,2 12,8]"], "7": ["[14,14 15,14]"]}, "output_rectangles": {"6": ["[11,2 12,8]"]}, "input_rect_count": 7, "output_rect_count": 1}, {"example_id": 2, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"3": ["[5,11 5,12]", "[10,10 10,11]"], "5": ["[12,10 13,10]"], "8": ["[2,5 2,6]"]}, "output_rectangles": {"7": ["[2,8 4,10]"]}, "input_rect_count": 4, "output_rect_count": 1}], "test_examples": [{"example_id": 0, "input_shape": [16, 16], "input_rectangles": {"1": ["[6,1 6,2]", "[12,6 13,6]"], "3": ["[4,10 5,10]"], "7": ["[14,6 14,7]"]}, "input_rect_count": 4}]}, "9edfc990": {"task_id": "9edfc990", "train_examples": [{"example_id": 0, "input_shape": [13, 13], "output_shape": [13, 13], "input_rectangles": {"0": ["[5,11 5,12]"], "5": ["[8,6 9,6]"], "9": ["[8,0 8,1]", "[10,6 10,7]"]}, "output_rectangles": {"0": ["[5,11 5,12]"], "5": ["[8,6 9,6]"], "9": ["[8,0 8,1]", "[10,6 10,7]"]}, "input_rect_count": 4, "output_rect_count": 4}, {"example_id": 1, "input_shape": [15, 15], "output_shape": [15, 15], "input_rectangles": {"0": ["[2,3 3,3]", "[4,13 4,14]", "[13,4 14,4]"], "2": ["[7,4 8,4]"], "3": ["[8,0 8,1]", "[12,8 12,9]"], "4": ["[1,2 1,3]", "[10,4 11,4]"], "5": ["[0,6 0,8]"], "8": ["[9,14 10,14]", "[14,2 14,3]"], "9": ["[0,4 1,4]", "[13,6 13,7]"]}, "output_rectangles": {"0": ["[4,13 4,14]", "[13,4 14,4]"], "2": ["[7,4 8,4]"], "3": ["[8,0 8,1]", "[12,8 12,9]"], "4": ["[1,2 1,3]", "[10,4 11,4]"], "5": ["[0,6 0,8]"], "8": ["[9,14 10,14]", "[14,2 14,3]"], "9": ["[0,4 1,4]", "[13,6 13,7]"]}, "input_rect_count": 13, "output_rect_count": 12}, {"example_id": 2, "input_shape": [16, 16], "output_shape": [16, 16], "input_rectangles": {"0": ["[9,9 9,10]", "[11,14 11,15]", "[13,0 15,0]"], "1": ["[0,8 0,10]", "[1,0 1,1]", "[6,5 7,5]", "[13,6 14,6]"], "2": ["[10,12 11,12]"], "3": ["[1,2 1,3]", "[10,4 11,4]", "[12,8 12,9]"], "4": ["[0,1 0,2]", "[0,4 1,4]", "[13,12 13,13]"], "5": ["[9,1 10,1]"], "6": ["[6,0 6,1]", "[8,5 8,6]"], "7": ["[10,14 10,15]"], "8": ["[1,13 2,13]", "[1,15 3,15]", "[2,3 2,4]"], "9": ["[8,8 9,8]", "[15,7 15,8]"]}, "output_rectangles": {"0": ["[13,0 15,0]"], "1": ["[14,10 15,10]"], "2": ["[10,12 11,12]"], "3": ["[1,2 1,3]", "[10,4 11,4]", "[12,8 12,9]"], "4": ["[0,1 0,2]", "[0,4 1,4]", "[13,12 13,13]"], "5": ["[9,1 10,1]"], "6": ["[6,0 6,1]", "[8,5 8,6]"], "7": ["[10,14 10,15]"], "8": ["[1,13 2,13]", "[1,15 3,15]", "[2,3 2,4]"], "9": ["[8,8 9,8]", "[15,7 15,8]"]}, "input_rect_count": 23, "output_rect_count": 18}], "test_examples": [{"example_id": 0, "input_shape": [16, 16], "input_rectangles": {"0": ["[0,0 0,2]", "[4,1 4,2]", "[6,2 7,2]"], "2": ["[4,4 4,5]", "[14,8 15,8]"], "6": ["[5,14 5,15]"], "7": ["[14,4 14,5]"], "9": ["[6,0 6,1]", "[10,4 11,4]"]}, "input_rect_count": 9}]}, "b9b7f026": {"task_id": "b9b7f026", "train_examples": [{"example_id": 0, "input_shape": [15, 13], "output_shape": [1, 1], "input_rectangles": {"0": ["[2,2 3,2]"], "1": ["[3,5 4,6]"], "2": ["[7,3 9,7]"], "3": ["[1,8 3,10]"], "4": ["[11,0 12,2]"], "7": ["[5,9 7,11]"], "8": ["[11,8 13,11]"]}, "output_rectangles": {}, "input_rect_count": 7, "output_rect_count": 0}, {"example_id": 1, "input_shape": [15, 17], "output_shape": [1, 1], "input_rectangles": {"0": ["[6,8 7,9]"], "2": ["[6,3 11,5]"], "4": ["[11,10 14,14]"], "7": ["[0,13 5,16]"], "8": ["[2,0 4,4]"]}, "output_rectangles": {}, "input_rect_count": 5, "output_rect_count": 0}, {"example_id": 2, "input_shape": [16, 17], "output_shape": [1, 1], "input_rectangles": {"0": ["[11,3 12,5]"], "1": ["[1,2 5,7]"], "3": ["[2,10 7,14]"], "7": ["[9,11 12,15]"]}, "output_rectangles": {}, "input_rect_count": 4, "output_rect_count": 0}], "test_examples": [{"example_id": 0, "input_shape": [14, 15], "input_rectangles": {"0": ["[12,11 12,12]"], "2": ["[1,0 6,7]"], "3": ["[0,10 4,13]"], "4": ["[6,9 9,12]"], "5": ["[8,1 10,3]"], "8": ["[9,4 12,7]"]}, "input_rect_count": 6}]}, "d687bc17": {"task_id": "d687bc17", "train_examples": [{"example_id": 0, "input_shape": [10, 15], "output_shape": [10, 15], "input_rectangles": {"2": ["[1,0 8,0]"], "3": ["[1,14 8,14]"], "4": ["[0,1 0,13]"], "8": ["[9,1 9,13]"]}, "output_rectangles": {}, "input_rect_count": 4, "output_rect_count": 0}, {"example_id": 1, "input_shape": [12, 12], "output_shape": [12, 12], "input_rectangles": {"1": ["[0,1 0,10]"], "2": ["[1,0 10,0]"], "4": ["[1,11 10,11]"], "7": ["[11,1 11,10]"]}, "output_rectangles": {}, "input_rect_count": 4, "output_rect_count": 0}, {"example_id": 2, "input_shape": [14, 11], "output_shape": [14, 11], "input_rectangles": {"3": ["[13,1 13,9]"], "4": ["[1,0 12,0]"], "6": ["[0,1 0,9]"], "8": ["[1,10 12,10]"]}, "output_rectangles": {"3": ["[13,1 13,9]"]}, "input_rect_count": 4, "output_rect_count": 1}], "test_examples": [{"example_id": 0, "input_shape": [14, 17], "input_rectangles": {"1": ["[1,0 12,0]"], "2": ["[1,16 12,16]"], "4": ["[0,1 0,15]"], "8": ["[13,1 13,15]"]}, "input_rect_count": 4}]}}