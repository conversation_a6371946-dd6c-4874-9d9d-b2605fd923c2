"""
Script d'entraînement pour le HRM amélioré
Utilise les techniques du paper : deep supervision, Q-learning ACT, gradient 1-step
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, random_split
import argparse
import os
from datetime import datetime

from config import Config
from src.arc_dataset import ArcDataset
from src.tokenizer import GrammarTokenizer
from tests.test_improved_hrm import ImprovedHRM
from engine.improved_trainer import ImprovedHRMTrainer

def setup_training_environment():
    """Configure l'environnement d'entraînement"""
    # CUDA optimizations
    if torch.cuda.is_available():
        print("[CONFIG] CUDA disponible - Configuration GPU optimisée")
        
        # TF32 pour accélération sur Ampere
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        
        # Optimisations mémoire
        torch.cuda.set_per_process_memory_fraction(0.9)
        torch.cuda.empty_cache()
        
        # Optimisations cuDNN
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        print(f"[CONFIG] GPU: {torch.cuda.get_device_name(0)}")
        print(f"[CONFIG] Mémoire: {torch.cuda.get_device_properties(0).total_memory/1e9:.2f} GB")
    else:
        print("[CONFIG] CUDA non disponible - Utilisation CPU")

def create_improved_model(cfg, tokenizer):
    """Crée le modèle HRM amélioré"""
    model = ImprovedHRM(
        model_dim=cfg.MODEL_DIM,
        n_heads=cfg.N_HEADS,
        grammar_vocab_size=len(tokenizer.vocab),
        max_grid_size=cfg.MAX_GRID_SIZE,
        N_cycles=cfg.N_CYCLES,
        T_steps=cfg.T_STEPS
    )
    
    print(f"Modèle créé: {model.count_parameters():.1f}M paramètres")
    print(f"Architecture: {cfg.N_CYCLES} cycles × {cfg.T_STEPS} étapes = {cfg.N_CYCLES * cfg.T_STEPS} profondeur effective")
    
    return model

def prepare_datasets(cfg, tokenizer, train_ratio=0.8):
    """Prépare les datasets d'entraînement et de validation"""
    print(f"Chargement du dataset depuis {cfg.DATA_DIR}")
    
    # Dataset complet
    full_dataset = ArcDataset(cfg.DATA_DIR, tokenizer, cfg.MAX_PROG_LEN)
    print(f"Dataset chargé: {len(full_dataset)} exemples")
    
    # Split train/validation
    train_size = int(train_ratio * len(full_dataset))
    val_size = len(full_dataset) - train_size
    
    train_dataset, val_dataset = random_split(
        full_dataset, 
        [train_size, val_size],
        generator=torch.Generator().manual_seed(42)  # Reproductibilité
    )
    
    print(f"Split: {len(train_dataset)} entraînement, {len(val_dataset)} validation")
    
    # DataLoaders
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=cfg.BATCH_SIZE,
        shuffle=True,
        num_workers=2,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=cfg.BATCH_SIZE,
        shuffle=False,
        num_workers=2,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    return train_dataloader, val_dataloader

def load_pretrained_model(model, checkpoint_path):
    """Charge un modèle pré-entraîné si disponible"""
    if os.path.exists(checkpoint_path):
        print(f"Chargement du modèle pré-entraîné: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # Charger les poids du modèle
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            print("Poids du modèle chargés avec succès")
        else:
            model.load_state_dict(checkpoint)
            print("Poids du modèle chargés (format simple)")
        
        return True
    else:
        print(f"Aucun modèle pré-entraîné trouvé à {checkpoint_path}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Entraînement HRM Amélioré")
    parser.add_argument("--epochs", type=int, default=50, help="Nombre d'époques")
    parser.add_argument("--batch-size", type=int, default=None, help="Taille de batch")
    parser.add_argument("--learning-rate", type=float, default=3e-4, help="Taux d'apprentissage")
    parser.add_argument("--resume", type=str, default=None, help="Reprendre depuis un checkpoint")
    parser.add_argument("--save-every", type=int, default=10, help="Sauvegarder tous les N époques")
    parser.add_argument("--validate-every", type=int, default=5, help="Valider tous les N époques")
    parser.add_argument("--max-segments", type=int, default=3, help="Nombre max de segments ACT")
    
    args = parser.parse_args()
    
    # Configuration
    setup_training_environment()
    cfg = Config()
    
    # Override config avec arguments
    if args.batch_size:
        cfg.BATCH_SIZE = args.batch_size
    
    print(f"Configuration:")
    print(f"  Époques: {args.epochs}")
    print(f"  Batch size: {cfg.BATCH_SIZE}")
    print(f"  Learning rate: {args.learning_rate}")
    print(f"  Device: {cfg.DEVICE}")
    
    # Tokenizer
    tokenizer = GrammarTokenizer()
    print(f"Tokenizer: {len(tokenizer.vocab)} tokens")
    
    # Datasets
    train_dataloader, val_dataloader = prepare_datasets(cfg, tokenizer)
    
    # Modèle
    model = create_improved_model(cfg, tokenizer)
    
    # Charger modèle pré-entraîné si spécifié
    if args.resume:
        load_pretrained_model(model, args.resume)
    
    # Trainer
    trainer = ImprovedHRMTrainer(model, tokenizer, cfg.DEVICE)
    
    # Ajuster le learning rate si spécifié
    if args.learning_rate != 3e-4:
        for param_group in trainer.optimizer.param_groups:
            param_group['lr'] = args.learning_rate
        print(f"Learning rate ajusté à {args.learning_rate}")
    
    # Configuration du trainer
    trainer.max_segments = args.max_segments
    
    print(f"\n{'='*60}")
    print("DÉBUT DE L'ENTRAÎNEMENT HRM AMÉLIORÉ")
    print(f"{'='*60}")
    print(f"Techniques utilisées:")
    print(f"  ✓ Deep Supervision (supervision à chaque segment)")
    print(f"  ✓ Q-learning ACT (adaptive computation time)")
    print(f"  ✓ Gradient 1-step avec détachement")
    print(f"  ✓ Architecture moderne (RMSNorm, GLU)")
    print(f"  ✓ Validation grammaticale intégrée")
    print(f"{'='*60}\n")
    
    # Entraînement
    try:
        trainer.train(
            train_dataloader=train_dataloader,
            val_dataloader=val_dataloader,
            epochs=args.epochs,
            save_every=args.save_every
        )
        
        print(f"\n{'='*60}")
        print("ENTRAÎNEMENT TERMINÉ AVEC SUCCÈS")
        print(f"{'='*60}")
        
        # Résumé final
        if trainer.training_history:
            final_loss = trainer.training_history[-1]['avg_loss']
            final_quality = trainer.training_history[-1]['avg_quality_score']
            final_valid_ratio = trainer.training_history[-1]['valid_program_ratio']
            
            print(f"Résultats finaux:")
            print(f"  Loss finale: {final_loss:.4f}")
            print(f"  Score de qualité: {final_quality:.3f}")
            print(f"  Programmes valides: {final_valid_ratio:.1%}")
        
        if trainer.validation_history:
            best_val_quality = max(h['avg_quality_score'] for h in trainer.validation_history)
            print(f"  Meilleur score validation: {best_val_quality:.3f}")
        
    except KeyboardInterrupt:
        print("\nEntraînement interrompu par l'utilisateur")
        trainer.save_model("hrm_improved_interrupted.pth")
        print("Modèle sauvegardé: hrm_improved_interrupted.pth")
    
    except Exception as e:
        print(f"\nErreur pendant l'entraînement: {e}")
        trainer.save_model("hrm_improved_error.pth")
        print("Modèle sauvegardé: hrm_improved_error.pth")
        raise

if __name__ == "__main__":
    main()