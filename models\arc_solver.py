"""
ARCSolver - Pipeline principal pour DINO-HRM-ARC.

Orchestre l'ensemble du processus : DINO → ScenarioGeneralizer → HRM → Validation
pour résoudre les puzzles ARC de bout en bout.
"""

import torch
import numpy as np
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from models.dino_arc import DinoARC
from src.scenario_generalizer import ScenarioGeneralizer
from models.hrm_parameter_resolver import HRMParameterResolver
from src.validation_system import ValidationSystem, ValidationResult


class SolutionStatus(Enum):
    """Statuts possibles pour une solution."""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILURE = "failure"
    ERROR = "error"
    TIMEOUT = "timeout"


@dataclass
class SolutionResult:
    """Résultat complet d'une résolution de puzzle."""
    status: SolutionStatus
    solution_grid: Optional[np.ndarray]
    confidence: float
    execution_time: float
    
    # Détails du processus
    detected_category: str
    category_confidence: float
    generated_scenario: Optional[str]
    final_command: Optional[str]
    validation_report: Optional[Dict[str, Any]]
    
    # Métriques
    dino_time: float = 0.0
    scenario_time: float = 0.0
    hrm_time: float = 0.0
    validation_time: float = 0.0
    
    # Messages et erreurs
    messages: List[str] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.messages is None:
            self.messages = []
        if self.errors is None:
            self.errors = []


class ARCSolver:
    """
    Solveur principal pour puzzles ARC utilisant l'architecture DINO-HRM.
    
    Processus complet :
    1. Analyse DINO : Détection de patterns et catégorisation
    2. Génération de scénario : Template généralisé avec contraintes
    3. Résolution HRM : Instanciation des paramètres et exécution
    4. Validation : Vérification sur exemples train avec retry
    """
    
    def __init__(self, 
                 dino_model: Optional[DinoARC] = None,
                 scenario_generalizer: Optional[ScenarioGeneralizer] = None,
                 hrm_resolver: Optional[HRMParameterResolver] = None,
                 validation_system: Optional[ValidationSystem] = None,
                 timeout: float = 60.0):
        """
        Args:
            dino_model: Modèle DINO-ARC pour l'analyse
            scenario_generalizer: Généralisateur de scénarios
            hrm_resolver: Résolveur de paramètres HRM
            validation_system: Système de validation
            timeout: Timeout en secondes pour la résolution
        """
        # Initialiser les composants
        self.dino = dino_model or DinoARC()
        self.generalizer = scenario_generalizer or ScenarioGeneralizer()
        self.hrm = hrm_resolver or HRMParameterResolver()
        self.validator = validation_system or ValidationSystem()
        
        self.timeout = timeout
        
        # Statistiques
        self.stats = {
            'total_puzzles': 0,
            'successful_solutions': 0,
            'partial_solutions': 0,
            'failed_solutions': 0,
            'error_solutions': 0,
            'timeout_solutions': 0,
            'average_time': 0.0,
            'category_distribution': {},
            'success_by_category': {}
        }
    
    def solve_puzzle(self, puzzle_data: Dict[str, Any]) -> SolutionResult:
        """
        Résout un puzzle ARC complet.
        
        Args:
            puzzle_data: Dict contenant:
                - train_pairs: List[Tuple[np.ndarray, np.ndarray]]
                - test_input: np.ndarray
                - puzzle_id: str (optionnel)
        
        Returns:
            SolutionResult avec la solution et les détails du processus
        """
        start_time = time.time()
        puzzle_id = puzzle_data.get('puzzle_id', 'unknown')
        
        print(f"\n{'='*60}")
        print(f"🧩 Résolution du puzzle: {puzzle_id}")
        print(f"{'='*60}")
        
        result = SolutionResult(
            status=SolutionStatus.ERROR,
            solution_grid=None,
            confidence=0.0,
            execution_time=0.0,
            detected_category="unknown",
            category_confidence=0.0,
            generated_scenario=None,
            final_command=None,
            validation_report=None
        )
        
        try:
            # Vérifier le timeout
            if time.time() - start_time > self.timeout:
                result.status = SolutionStatus.TIMEOUT
                result.errors.append("Timeout avant le début du traitement")
                return result
            
            # Étape 1: Analyse DINO
            print("\n🔍 Étape 1: Analyse DINO")
            dino_start = time.time()
            
            dino_result = self._run_dino_analysis(puzzle_data)
            
            result.dino_time = time.time() - dino_start
            result.detected_category = dino_result['category']
            result.category_confidence = dino_result['confidence']
            
            print(f"   Catégorie détectée: {result.detected_category}")
            print(f"   Confiance: {result.category_confidence:.2f}")
            print(f"   Temps DINO: {result.dino_time:.2f}s")
            
            # Vérifier le timeout
            if time.time() - start_time > self.timeout:
                result.status = SolutionStatus.TIMEOUT
                return result
            
            # Étape 2: Génération de scénario
            print("\n📋 Étape 2: Génération de scénario")
            scenario_start = time.time()
            
            scenario = self._generate_scenario(dino_result)
            
            result.scenario_time = time.time() - scenario_start
            result.generated_scenario = scenario.template
            
            print(f"   Template: {scenario.template}")
            print(f"   Contraintes: {len(scenario.constraints)}")
            print(f"   Temps génération: {result.scenario_time:.2f}s")
            
            # Vérifier le timeout
            if time.time() - start_time > self.timeout:
                result.status = SolutionStatus.TIMEOUT
                return result
            
            # Étape 3: Résolution HRM
            print("\n⚙️ Étape 3: Résolution HRM")
            hrm_start = time.time()
            
            hrm_result = self._run_hrm_resolution(
                scenario, 
                puzzle_data['test_input'], 
                puzzle_data['train_pairs']
            )
            
            result.hrm_time = time.time() - hrm_start
            result.final_command = hrm_result.get('final_command')
            
            print(f"   Commande finale: {result.final_command}")
            print(f"   Temps HRM: {result.hrm_time:.2f}s")
            
            # Vérifier le timeout
            if time.time() - start_time > self.timeout:
                result.status = SolutionStatus.TIMEOUT
                return result
            
            # Étape 4: Validation
            print("\n✅ Étape 4: Validation")
            validation_start = time.time()
            
            validation_report = self._run_validation(
                scenario,
                puzzle_data['test_input'],
                puzzle_data['train_pairs']
            )
            
            result.validation_time = time.time() - validation_start
            result.validation_report = {
                'result': validation_report.result.value,
                'success_rate': validation_report.success_rate,
                'successful_examples': validation_report.successful_examples,
                'total_examples': validation_report.total_examples,
                'execution_time': validation_report.execution_time
            }
            
            print(f"   Statut validation: {validation_report.result.value}")
            print(f"   Taux de succès: {validation_report.success_rate:.1%}")
            print(f"   Temps validation: {result.validation_time:.2f}s")
            
            # Déterminer le résultat final
            result.solution_grid = validation_report.final_result
            result.confidence = validation_report.success_rate * result.category_confidence
            
            if validation_report.result == ValidationResult.SUCCESS:
                result.status = SolutionStatus.SUCCESS
                result.messages.append("Solution trouvée avec succès")
            elif validation_report.result == ValidationResult.PARTIAL_SUCCESS:
                result.status = SolutionStatus.PARTIAL_SUCCESS
                result.messages.append("Solution partielle trouvée")
            else:
                result.status = SolutionStatus.FAILURE
                result.errors.append("Échec de la validation")
            
        except Exception as e:
            result.status = SolutionStatus.ERROR
            result.errors.append(f"Erreur durant la résolution: {str(e)}")
            print(f"❌ Erreur: {e}")
        
        finally:
            result.execution_time = time.time() - start_time
            self._update_statistics(result)
            
            print(f"\n📊 Résultat final:")
            print(f"   Statut: {result.status.value}")
            print(f"   Confiance: {result.confidence:.2f}")
            print(f"   Temps total: {result.execution_time:.2f}s")
            
            if result.solution_grid is not None:
                print(f"   Solution trouvée: {result.solution_grid.shape}")
            
            print(f"{'='*60}")
        
        return result
    
    def _run_dino_analysis(self, puzzle_data: Dict[str, Any]) -> Dict[str, Any]:
        """Exécute l'analyse DINO sur le puzzle."""
        train_pairs = puzzle_data['train_pairs']
        
        # Convertir en format attendu par DINO (avec batch dimension)
        dino_train_pairs = []
        for input_grid, output_grid in train_pairs:
            # Ajouter dimension batch si nécessaire
            if len(input_grid.shape) == 2:
                input_batch = torch.tensor(input_grid, dtype=torch.long).unsqueeze(0)
                output_batch = torch.tensor(output_grid, dtype=torch.long).unsqueeze(0)
            else:
                input_batch = torch.tensor(input_grid, dtype=torch.long)
                output_batch = torch.tensor(output_grid, dtype=torch.long)
            
            dino_train_pairs.append((input_batch, output_batch))
        
        # Analyse DINO
        with torch.no_grad():
            dino_result = self.dino(dino_train_pairs)
        
        return dino_result
    
    def _generate_scenario(self, dino_result: Dict[str, Any]):
        """Génère un scénario généralisé basé sur l'analyse DINO."""
        category = dino_result['category']
        confidence = dino_result['confidence']
        
        scenario = self.generalizer.generate_scenario(category, confidence)
        
        return scenario
    
    def _run_hrm_resolution(self, 
                           scenario,
                           test_input: np.ndarray,
                           train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> Dict[str, Any]:
        """Exécute la résolution HRM."""
        try:
            # Résoudre les paramètres
            resolved_params = self.hrm.resolve_parameters(scenario, test_input, train_pairs)
            
            # Instancier le template
            final_command = self.hrm.instantiate_template(scenario.template, resolved_params)
            
            return {
                'success': True,
                'final_command': final_command,
                'resolved_params': resolved_params
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'final_command': None
            }
    
    def _run_validation(self, 
                       scenario,
                       test_input: np.ndarray,
                       train_pairs: List[Tuple[np.ndarray, np.ndarray]]):
        """Exécute la validation complète."""
        validation_report = self.validator.validate_solution(
            scenario, test_input, train_pairs
        )
        
        return validation_report
    
    def _update_statistics(self, result: SolutionResult):
        """Met à jour les statistiques globales."""
        self.stats['total_puzzles'] += 1
        
        # Compter par statut
        if result.status == SolutionStatus.SUCCESS:
            self.stats['successful_solutions'] += 1
        elif result.status == SolutionStatus.PARTIAL_SUCCESS:
            self.stats['partial_solutions'] += 1
        elif result.status == SolutionStatus.FAILURE:
            self.stats['failed_solutions'] += 1
        elif result.status == SolutionStatus.ERROR:
            self.stats['error_solutions'] += 1
        elif result.status == SolutionStatus.TIMEOUT:
            self.stats['timeout_solutions'] += 1
        
        # Distribution par catégorie
        category = result.detected_category
        if category not in self.stats['category_distribution']:
            self.stats['category_distribution'][category] = 0
            self.stats['success_by_category'][category] = 0
        
        self.stats['category_distribution'][category] += 1
        
        if result.status in [SolutionStatus.SUCCESS, SolutionStatus.PARTIAL_SUCCESS]:
            self.stats['success_by_category'][category] += 1
        
        # Temps moyen
        total_time = sum([
            r.execution_time for r in [result] 
            if hasattr(r, 'execution_time')
        ])
        self.stats['average_time'] = (
            (self.stats['average_time'] * (self.stats['total_puzzles'] - 1) + result.execution_time) 
            / self.stats['total_puzzles']
        )
    
    def solve_multiple_puzzles(self, 
                              puzzles: List[Dict[str, Any]], 
                              max_puzzles: Optional[int] = None) -> List[SolutionResult]:
        """
        Résout plusieurs puzzles en séquence.
        
        Args:
            puzzles: Liste de données de puzzles
            max_puzzles: Nombre maximum de puzzles à traiter
        
        Returns:
            Liste des résultats de résolution
        """
        if max_puzzles:
            puzzles = puzzles[:max_puzzles]
        
        print(f"\n🚀 Résolution de {len(puzzles)} puzzles")
        print("=" * 60)
        
        results = []
        
        for i, puzzle_data in enumerate(puzzles):
            print(f"\n📍 Puzzle {i+1}/{len(puzzles)}")
            
            result = self.solve_puzzle(puzzle_data)
            results.append(result)
            
            # Afficher un résumé intermédiaire
            if (i + 1) % 10 == 0:
                self._print_intermediate_stats(i + 1)
        
        # Afficher les statistiques finales
        self._print_final_stats()
        
        return results
    
    def _print_intermediate_stats(self, processed: int):
        """Affiche des statistiques intermédiaires."""
        success_rate = (
            (self.stats['successful_solutions'] + self.stats['partial_solutions']) 
            / self.stats['total_puzzles'] * 100
        )
        
        print(f"\n📈 Statistiques intermédiaires ({processed} puzzles):")
        print(f"   Taux de succès: {success_rate:.1f}%")
        print(f"   Temps moyen: {self.stats['average_time']:.2f}s")
    
    def _print_final_stats(self):
        """Affiche les statistiques finales."""
        total = self.stats['total_puzzles']
        if total == 0:
            return
        
        print(f"\n📊 STATISTIQUES FINALES")
        print("=" * 40)
        print(f"Total puzzles traités: {total}")
        print(f"Succès complets: {self.stats['successful_solutions']} ({self.stats['successful_solutions']/total*100:.1f}%)")
        print(f"Succès partiels: {self.stats['partial_solutions']} ({self.stats['partial_solutions']/total*100:.1f}%)")
        print(f"Échecs: {self.stats['failed_solutions']} ({self.stats['failed_solutions']/total*100:.1f}%)")
        print(f"Erreurs: {self.stats['error_solutions']} ({self.stats['error_solutions']/total*100:.1f}%)")
        print(f"Timeouts: {self.stats['timeout_solutions']} ({self.stats['timeout_solutions']/total*100:.1f}%)")
        print(f"Temps moyen: {self.stats['average_time']:.2f}s")
        
        print(f"\n📋 Distribution par catégorie:")
        for category, count in self.stats['category_distribution'].items():
            success_count = self.stats['success_by_category'].get(category, 0)
            success_rate = success_count / count * 100 if count > 0 else 0
            print(f"   {category}: {count} puzzles ({success_rate:.1f}% succès)")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques actuelles."""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Remet à zéro les statistiques."""
        self.stats = {
            'total_puzzles': 0,
            'successful_solutions': 0,
            'partial_solutions': 0,
            'failed_solutions': 0,
            'error_solutions': 0,
            'timeout_solutions': 0,
            'average_time': 0.0,
            'category_distribution': {},
            'success_by_category': {}
        }
    
    def save_results(self, results: List[SolutionResult], filepath: str):
        """Sauvegarde les résultats dans un fichier JSON."""
        import json
        
        # Convertir les résultats en format sérialisable
        serializable_results = []
        
        for result in results:
            result_dict = {
                'status': result.status.value,
                'confidence': result.confidence,
                'execution_time': result.execution_time,
                'detected_category': result.detected_category,
                'category_confidence': result.category_confidence,
                'generated_scenario': result.generated_scenario,
                'final_command': result.final_command,
                'validation_report': result.validation_report,
                'dino_time': result.dino_time,
                'scenario_time': result.scenario_time,
                'hrm_time': result.hrm_time,
                'validation_time': result.validation_time,
                'messages': result.messages,
                'errors': result.errors
            }
            
            # Convertir la grille solution en liste si elle existe
            if result.solution_grid is not None:
                result_dict['solution_grid'] = result.solution_grid.tolist()
            else:
                result_dict['solution_grid'] = None
            
            serializable_results.append(result_dict)
        
        # Sauvegarder avec les statistiques
        output_data = {
            'results': serializable_results,
            'statistics': self.get_statistics(),
            'timestamp': time.time()
        }
        
        with open(filepath, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"💾 Résultats sauvegardés dans: {filepath}")


# Fonctions utilitaires pour l'utilisation du solver

def create_puzzle_data_from_dataset(dataset, index: int) -> Dict[str, Any]:
    """
    Crée des données de puzzle à partir d'un dataset.
    
    Args:
        dataset: Dataset (MultiExampleDataset ou similaire)
        index: Index du puzzle dans le dataset
    
    Returns:
        Dict avec train_pairs, test_input, puzzle_id
    """
    sample = dataset[index]
    
    return {
        'train_pairs': sample['train_pairs'],
        'test_input': sample['test_input'],
        'puzzle_id': sample['puzzle_id']
    }


def create_puzzle_data_from_json(puzzle_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    Crée des données de puzzle à partir d'un JSON ARC.
    
    Args:
        puzzle_json: Données JSON du puzzle ARC
    
    Returns:
        Dict avec train_pairs, test_input, puzzle_id
    """
    train_pairs = []
    
    for example in puzzle_json['train']:
        input_grid = np.array(example['input'])
        output_grid = np.array(example['output'])
        train_pairs.append((input_grid, output_grid))
    
    test_input = np.array(puzzle_json['test'][0]['input'])
    
    return {
        'train_pairs': train_pairs,
        'test_input': test_input,
        'puzzle_id': puzzle_json.get('id', 'unknown')
    }