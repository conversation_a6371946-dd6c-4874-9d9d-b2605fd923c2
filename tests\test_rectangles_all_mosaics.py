import numpy as np
import json
import os

def all_rectangles(mat):
    mat = np.asarray(mat)
    results = {}
    
    # Trouver la couleur de fond (celle avec le plus de cellules)
    unique_values, counts = np.unique(mat, return_counts=True)
    background_color = unique_values[np.argmax(counts)]
    
    for val in np.unique(mat):
        # Ignorer la couleur de fond
        if val == background_color:
            continue
            
        rects = []
        for r1 in range(mat.shape[0]):
            for c1 in range(mat.shape[1]):
                for r2 in range(r1, mat.shape[0]):
                    for c2 in range(c1, mat.shape[1]):
                        # Vérification : si le rectangle extrait est bien plein de val
                        # ET qu'il fait plus de 4 cellules
                        rect_size = (r2 - r1 + 1) * (c2 - c1 + 1)
                        if np.all(mat[r1:r2+1, c1:c2+1] == val) and rect_size > 4:
                            rects.append((r1, c1, r2, c2))
        if rects:
            # Fusionner les rectangles qui se chevauchent
            merged_rects = merge_overlapping_rectangles(rects)
            # Retourner juste les coordonnées
            results[int(val)] = [f"[{r1},{c1} {r2},{c2}]" for r1, c1, r2, c2 in merged_rects]
    return results

def rectangles_overlap(rect1, rect2):
    """Vérifie si deux rectangles se chevauchent"""
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    return not (r2_1 < r1_2 or r2_2 < r1_1 or c2_1 < c1_2 or c2_2 < c1_1)

def merge_two_rectangles(rect1, rect2):
    """Fusionne deux rectangles en un rectangle englobant"""
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    return (min(r1_1, r1_2), min(c1_1, c1_2), max(r2_1, r2_2), max(c2_1, c2_2))

def merge_overlapping_rectangles(rectangles):
    """Fusionne tous les rectangles qui se chevauchent"""
    if not rectangles:
        return []
    
    merged = []
    used = set()
    
    for i, rect1 in enumerate(rectangles):
        if i in used:
            continue
        
        # Commencer avec le rectangle actuel
        current_merged = rect1
        overlapping_indices = {i}
        
        # Continuer à fusionner tant qu'on trouve des chevauchements
        changed = True
        while changed:
            changed = False
            for j, rect2 in enumerate(rectangles):
                if j not in overlapping_indices and rectangles_overlap(current_merged, rect2):
                    current_merged = merge_two_rectangles(current_merged, rect2)
                    overlapping_indices.add(j)
                    changed = True
        
        merged.append(current_merged)
        used.update(overlapping_indices)
    
    return merged

def apply_color_mask(mat, rectangles, target_color):
    """Applique un masque true/false pour chaque rectangle"""
    results = []
    
    for r1, c1, r2, c2 in rectangles:
        # Créer le masque true/false pour ce rectangle
        height = r2 - r1 + 1
        width = c2 - c1 + 1
        mask = []
        
        for r in range(r1, r2 + 1):
            row_mask = []
            for c in range(c1, c2 + 1):
                row_mask.append(mat[r, c] == target_color)
            mask.append(row_mask)
        
        # Formater le résultat
        rect_info = f"[{r1},{c1} {r2},{c2}]"
        mask_str = " MASK(" + str(mask).replace("True", "T").replace("False", "F") + ")"
        results.append(rect_info + mask_str)
    
    return results

def load_arc_data():
    training_path = "arcdata/training"
    evaluation_path = "arcdata/evaluation"
    data = {}
    
    if os.path.exists(training_path):
        for filename in os.listdir(training_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]
                try:
                    with open(os.path.join(training_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except:
                    pass
    
    if os.path.exists(evaluation_path):
        for filename in os.listdir(evaluation_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]
                try:
                    with open(os.path.join(evaluation_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except:
                    pass
    
    return data

def get_mosaic_puzzle_ids():
    mosaic_ids = []
    encodings = ['utf-8', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            with open("AnalysesGrilles/final_mosaic_puzzle_ids.txt", 'r', encoding=encoding) as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        task_id = line.split()[0]
                        if len(task_id) == 8:
                            mosaic_ids.append(task_id)
            break
        except:
            continue
    
    return mosaic_ids

# Test sur tous les inputs des puzzles mosaïques
arc_data = load_arc_data()
mosaic_ids = get_mosaic_puzzle_ids()

print(f"=== TEST ALGORITHME RECTANGLES SUR {len(mosaic_ids)} PUZZLES MOSAÏQUES ===\n")

total_rectangles = 0
total_grids = 0

for task_id in mosaic_ids:
    if task_id not in arc_data:
        continue
    
    task_data = arc_data[task_id]
    print(f"--- {task_id} ---")
    
    # Exemples d'entraînement
    for i, example in enumerate(task_data.get('train', [])):
        input_grid = np.array(example['input'])
        zones = all_rectangles(input_grid)
        if zones:
            print(f"Train {i+1} input ({input_grid.shape}):")
            for val, rects in zones.items():
                print(f"  Couleur {val}: {rects}")
                total_rectangles += len(rects)
            total_grids += 1
    
    # Exemples de test
    for i, example in enumerate(task_data.get('test', [])):
        input_grid = np.array(example['input'])
        zones = all_rectangles(input_grid)
        if zones:
            print(f"Test {i+1} input ({input_grid.shape}):")
            for val, rects in zones.items():
                print(f"  Couleur {val}: {rects}")
                total_rectangles += len(rects)
            total_grids += 1
    
    print()

print(f"=== RÉSUMÉ ===")
print(f"Total rectangles détectés: {total_rectangles}")
print(f"Total grilles analysées: {total_grids}")
if total_grids > 0:
    print(f"Moyenne rectangles par grille: {total_rectangles / total_grids:.2f}")