import torch
from torch.utils.data import Dataset
import json
import os
from src.tokenizer import GrammarTokenizer # Correction de l'import

class ArcDataset(Dataset):
    def __init__(self, data_folder: str, tokenizer: GrammarTokenizer, max_prog_len: int = 200):
        self.data = []
        self.tokenizer = tokenizer # Utilisation du tokenizer fourni
        self.max_prog_len = max_prog_len
        
        # Parcourir les fichiers dans le dossier
        for filename in os.listdir(data_folder):
            if filename.endswith('.json'):
                puzzle_id = filename.split('.')[0]
                json_path = os.path.join(data_folder, filename)
                program_path = os.path.join(data_folder, f"{puzzle_id}_TEST0_VALID.agi")
                
                if not os.path.exists(program_path):
                    continue
                
                # Charger le puzzle
                with open(json_path, 'r') as f:
                    puzzle_data = json.load(f)
                
                # Charger le programme
                with open(program_path, 'r') as f:
                    program = f.read().strip()
                
                # Simplifier le programme : remplacer TRANSFERT {...} par juste TRANSFERT
                program = self.simplify_transfert(program)
                
                # Supprimer END car redondant avec <eos>
                program = self.remove_end_token(program)
                
                # Décomposer les commandes groupées (ACTIONS) en commandes individuelles
                program = self.decompose_grouped_commands(program)
                
                # Prendre le premier exemple d'entraînement
                input_grid = puzzle_data['train'][0]['input']
                
                self.data.append({
                    'input_grid': input_grid,
                    'program': program
                })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        input_grid = item['input_grid']
        program = item['program']
        
        # Convertir la grille en tensor et padder à une taille fixe
        grid_tensor = torch.tensor(input_grid, dtype=torch.long)
        
        # Padder la grille à 30x30 (taille max ARC)
        max_size = 30
        h, w = grid_tensor.shape
        padded_grid = torch.zeros((max_size, max_size), dtype=torch.long)
        padded_grid[:h, :w] = grid_tensor
        
        # Tokeniser le programme
        prog_tokens = self.tokenizer.tokenize(program)
        prog_tokens = [self.tokenizer.vocab['<sos>']] + prog_tokens + [self.tokenizer.vocab['<eos>']]
        if len(prog_tokens) > self.max_prog_len:
            prog_tokens = prog_tokens[:self.max_prog_len]
        else:
            prog_tokens = prog_tokens + [self.tokenizer.vocab['<pad>']] * (self.max_prog_len - len(prog_tokens))
        
        return padded_grid, torch.tensor(prog_tokens, dtype=torch.long)
    
    def simplify_transfert(self, program: str) -> str:
        """
        Simplifie les blocs TRANSFERT en gardant seulement le mot TRANSFERT.
        
        Transforme:
        TRANSFERT {INIT 3x3; EDIT 7 ([0,0] [0,2] ...)}
        
        En:
        TRANSFERT
        """
        import re
        
        # Pattern pour matcher TRANSFERT {...}
        # Utilise un compteur de {} pour gérer les accolades imbriquées
        pattern = r'TRANSFERT\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        
        # Remplacer par juste TRANSFERT
        simplified = re.sub(pattern, 'TRANSFERT', program)
        
        return simplified
    
    def remove_end_token(self, program: str) -> str:
        """
        Supprime le token END du programme car redondant avec <eos>.
        
        Transforme:
        TRANSFERT RESIZE 9x9 END
        
        En:
        TRANSFERT RESIZE 9x9
        """
        # Supprimer END en fin de programme (avec espaces optionnels)
        program = program.rstrip()
        if program.endswith(' END'):
            program = program[:-4]
        elif program.endswith('END'):
            program = program[:-3]
        
        return program.strip()
    
    def decompose_grouped_commands(self, program: str) -> str:
        """
        Décompose les commandes groupées (ACTIONS) en commandes individuelles.
        
        Transforme:
        EDITS { EDIT 1 ([0,0] [1,1]); EDIT 2 ([2,2] [3,3])}
        
        En:
        EDIT 1 ([0,0] [1,1]) EDIT 2 ([2,2] [3,3])
        """
        import re
        
        # Mapping des commandes groupées vers leurs équivalents singuliers
        group_to_single = {
            'EDITS': 'EDIT',
            'FILLS': 'FILL', 
            'REPLACES': 'REPLACE',
            'CLEARS': 'CLEAR',
            'SURROUNDS': 'SURROUND',
            'FLIPS': 'FLIP',
            'ROTATES': 'ROTATE',
            'INSERTS': 'INSERT',
            'DELETES': 'DELETE',
            'FLOODFILLS': 'FLOODFILL'
        }
        
        for grouped_cmd, single_cmd in group_to_single.items():
            # Pattern pour matcher les commandes groupées avec accolades
            # Ex: EDITS { EDIT 1 ([0,0]); EDIT 2 ([2,2])}
            pattern = rf'{grouped_cmd}\s*\{{([^}}]*)\}}'
            
            def replace_grouped(match):
                content = match.group(1).strip()
                
                # Séparer les commandes individuelles par point-virgule
                commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
                
                # Remplacer chaque commande individuelle si nécessaire
                result = []
                for cmd in commands:
                    # Si la commande commence déjà par le bon nom, la garder
                    if cmd.startswith(single_cmd):
                        result.append(cmd)
                    else:
                        # Sinon, l'ajouter (cas où il n'y a que les paramètres)
                        result.append(f"{single_cmd} {cmd}")
                
                return '; '.join(result)
            
            program = re.sub(pattern, replace_grouped, program)
        
        return program