#!/usr/bin/env python3
"""
Script de test pour le ScenarioGeneralizer.

Valide que le généralisateur peut créer des templates généralisés
et définir les contraintes de résolution des paramètres.
"""

import sys
import os
sys.path.append('.')

from src.scenario_generalizer import ScenarioGeneralizer, GeneralizedScenario, ParameterType, ParameterConstraint


def test_basic_functionality():
    """Test des fonctionnalités de base du généralisateur."""
    print("=== Test ScenarioGeneralizer ===")
    
    # Créer le généralisateur
    generalizer = ScenarioGeneralizer()
    
    print(f"Nombre de catégories disponibles: {len(generalizer.get_available_categories())}")
    print(f"Catégories: {generalizer.get_available_categories()}")
    
    # Tester la génération de scénario pour une catégorie
    scenario = generalizer.generate_scenario('color_change', confidence=0.85)
    
    print(f"\nScénario pour 'color_change':")
    print(f"Template: {scenario.template}")
    print(f"Nombre de contraintes: {len(scenario.constraints)}")
    print(f"Confiance: {scenario.confidence}")
    
    for constraint in scenario.constraints:
        print(f"  - {constraint.param_name} ({constraint.param_type.value}): {constraint.constraint_type}")
    
    assert scenario.category == 'color_change', "Catégorie incorrecte"
    assert scenario.confidence == 0.85, "Confiance incorrecte"
    assert len(scenario.constraints) > 0, "Doit avoir des contraintes"
    
    print("✓ Génération de scénario OK")


def test_all_categories():
    """Test de génération pour toutes les catégories."""
    print("\n=== Test toutes les catégories ===")
    
    generalizer = ScenarioGeneralizer()
    categories = generalizer.get_available_categories()
    
    for category in categories:
        scenario = generalizer.generate_scenario(category)
        
        print(f"{category}: {scenario.template[:50]}...")
        
        # Vérifications de base
        assert scenario.category == category, f"Catégorie incorrecte pour {category}"
        assert isinstance(scenario.template, str), f"Template doit être une string pour {category}"
        assert isinstance(scenario.constraints, list), f"Contraintes doivent être une liste pour {category}"
    
    print(f"✓ {len(categories)} catégories testées")


def test_program_generalization():
    """Test de généralisation d'un programme spécifique."""
    print("\n=== Test généralisation de programme ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Programme spécifique
    specific_program = "EDIT 3 ([1,1] [2,2] [3,3])"
    
    # Généralisation
    scenario = generalizer.generalize_specific_program(specific_program)
    
    print(f"Programme original: {specific_program}")
    print(f"Template généralisé: {scenario.template}")
    print(f"Contraintes:")
    
    for constraint in scenario.constraints:
        print(f"  - {constraint.param_name}: {constraint.constraint_type} = {constraint.constraint_data}")
    
    # Vérifications
    assert '{' in scenario.template and '}' in scenario.template, "Template doit contenir des variables"
    assert len(scenario.constraints) > 0, "Doit avoir des contraintes"
    
    # Vérifier que les couleurs ont été remplacées
    assert '3' not in scenario.template or '{' in scenario.template, "Couleurs doivent être généralisées"
    
    print("✓ Généralisation de programme OK")


def test_scenario_validation():
    """Test de validation des scénarios."""
    print("\n=== Test validation de scénarios ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Scénario valide
    valid_scenario = generalizer.generate_scenario('color_change')
    validation = generalizer.validate_scenario(valid_scenario)
    
    print(f"Scénario valide: {validation['is_valid']}")
    print(f"Erreurs: {validation['errors']}")
    print(f"Avertissements: {validation['warnings']}")
    
    assert validation['is_valid'], "Scénario généré doit être valide"
    
    # Scénario invalide (template avec variable sans contrainte)
    invalid_scenario = GeneralizedScenario(
        template="EDIT {missing_var} {another_var}",
        constraints=[
            ParameterConstraint("another_var", ParameterType.COLOR, "test")
        ],
        category='test'
    )
    
    invalid_validation = generalizer.validate_scenario(invalid_scenario)
    
    print(f"\nScénario invalide: {invalid_validation['is_valid']}")
    print(f"Erreurs: {invalid_validation['errors']}")
    
    assert not invalid_validation['is_valid'], "Scénario invalide doit être détecté"
    assert len(invalid_validation['errors']) > 0, "Doit avoir des erreurs"
    
    print("✓ Validation de scénarios OK")


def test_template_patterns():
    """Test des patterns dans les templates."""
    print("\n=== Test patterns de templates ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Vérifier que les templates contiennent les bonnes structures
    test_cases = [
        ('color_change', ['EDIT', '{target_color}']),
        ('shape_completion', ['FILL', '{fill_color}']),
        ('pattern_repetition', ['MOTIF', 'MULTIPLY']),
        ('symmetry', ['FLIP', '{flip_axis}']),
        ('rotation', ['ROTATE', '{rotation_angle}']),
    ]
    
    for category, expected_elements in test_cases:
        scenario = generalizer.generate_scenario(category)
        template = scenario.template.upper()
        
        print(f"{category}: {scenario.template}")
        
        for element in expected_elements:
            assert element.upper() in template, f"Template {category} doit contenir {element}"
    
    print("✓ Patterns de templates OK")


def test_constraint_types():
    """Test des types de contraintes."""
    print("\n=== Test types de contraintes ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Vérifier que tous les types de paramètres sont utilisés
    all_param_types = set()
    
    for category in generalizer.get_available_categories():
        scenario = generalizer.generate_scenario(category)
        for constraint in scenario.constraints:
            all_param_types.add(constraint.param_type)
    
    print(f"Types de paramètres utilisés: {[pt.value for pt in all_param_types]}")
    
    # Vérifier qu'on a une bonne couverture des types
    expected_types = {ParameterType.COLOR, ParameterType.PATTERN, ParameterType.POSITION}
    assert expected_types.issubset(all_param_types), "Types de base manquants"
    
    print("✓ Types de contraintes OK")


def test_export_import():
    """Test d'export/import des templates."""
    print("\n=== Test export/import ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Export
    export_file = "test_templates.json"
    generalizer.export_templates(export_file)
    
    print(f"Templates exportés vers {export_file}")
    
    # Créer un nouveau généralisateur et importer
    new_generalizer = ScenarioGeneralizer()
    new_generalizer.import_templates(export_file)
    
    # Vérifier que les templates sont identiques
    original_categories = set(generalizer.get_available_categories())
    imported_categories = set(new_generalizer.get_available_categories())
    
    assert original_categories == imported_categories, "Catégories doivent être identiques"
    
    # Vérifier un template spécifique
    original_scenario = generalizer.generate_scenario('color_change')
    imported_scenario = new_generalizer.generate_scenario('color_change')
    
    assert original_scenario.template == imported_scenario.template, "Templates doivent être identiques"
    assert len(original_scenario.constraints) == len(imported_scenario.constraints), "Contraintes doivent être identiques"
    
    # Nettoyer
    os.remove(export_file)
    
    print("✓ Export/import OK")


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Requirement 3.1: Remplacer couleurs spécifiques par variables
    specific_program = "EDIT 5 ([0,0] [1,1])"
    generalized = generalizer.generalize_specific_program(specific_program)
    
    assert '5' not in generalized.template or '{' in generalized.template, "Couleurs doivent être généralisées"
    color_constraints = [c for c in generalized.constraints if c.param_type == ParameterType.COLOR]
    assert len(color_constraints) > 0, "Doit avoir des contraintes de couleur"
    print("✓ Requirement 3.1: Remplacement couleurs OK")
    
    # Requirement 3.2: Remplacer coordonnées par patterns géométriques
    coord_constraints = [c for c in generalized.constraints if c.param_type == ParameterType.PATTERN]
    assert len(coord_constraints) > 0, "Doit avoir des contraintes de pattern"
    print("✓ Requirement 3.2: Remplacement coordonnées OK")
    
    # Requirement 3.3: Inclure contraintes de résolution
    scenario = generalizer.generate_scenario('color_change')
    assert len(scenario.constraints) > 0, "Doit avoir des contraintes"
    
    for constraint in scenario.constraints:
        assert hasattr(constraint, 'constraint_type'), "Contrainte doit avoir un type"
        assert constraint.constraint_type, "Type de contrainte ne peut pas être vide"
    
    print("✓ Requirement 3.3: Contraintes de résolution OK")


def main():
    """Fonction principale de test."""
    print("Test du ScenarioGeneralizer pour DINO-HRM-ARC")
    print("=" * 50)
    
    success = True
    
    try:
        test_basic_functionality()
        test_all_categories()
        test_program_generalization()
        test_scenario_validation()
        test_template_patterns()
        test_constraint_types()
        test_export_import()
        test_requirements_compliance()
        
        print("\n🎉 Tous les tests sont passés!")
        print("Le ScenarioGeneralizer est prêt pour DINO-HRM-ARC")
        
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)