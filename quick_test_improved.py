"""
Test rapide du HRM amélioré pour valider l'implémentation
"""

import torch
from config import Config
from src.tokenizer import GrammarTokenizer
from tests.test_improved_hrm import ImprovedHRM
from src.grammar_validator import validate_generated_program

def test_model_components():
    """Test des composants individuels du modèle"""
    print("=== Test des Composants HRM Amélioré ===")
    
    cfg = Config()
    tokenizer = GrammarTokenizer()
    
    # Modèle
    model = ImprovedHRM(
        model_dim=128,  # Plus petit pour test rapide
        n_heads=4,
        grammar_vocab_size=len(tokenizer.vocab),
        N_cycles=2,
        T_steps=3
    )
    
    print(f"✓ Modèle créé: {model.count_parameters():.1f}M paramètres")
    
    # Test données
    batch_size = 2
    grid_size = 10
    seq_len = 20
    
    dummy_grid = torch.randint(0, 10, (batch_size, grid_size, grid_size))
    dummy_program = torch.randint(1, len(tokenizer.vocab)-1, (batch_size, seq_len))
    
    print(f"✓ Données de test créées: grille {dummy_grid.shape}, programme {dummy_program.shape}")
    
    # Test forward avec deep supervision
    print("\n--- Test Forward avec Deep Supervision ---")
    segments, q_values, losses = model.forward_with_deep_supervision(
        dummy_grid, dummy_program, max_segments=2
    )
    
    print(f"✓ Segments générés: {len(segments)}")
    print(f"✓ Q-values: {len(q_values)}")
    print(f"✓ Losses: {len(losses)}")
    
    if segments:
        print(f"✓ Forme segment: {segments[0].shape}")
    if q_values:
        print(f"✓ Forme Q-values: {q_values[0].shape}")
    
    # Test génération
    print("\n--- Test Génération de Programme ---")
    generated_tokens = model.generate_program(dummy_grid[0], max_length=50)
    program_str = tokenizer.detokenize(generated_tokens)
    
    print(f"✓ Tokens générés: {len(generated_tokens)}")
    print(f"✓ Programme: {program_str[:100]}...")
    
    # Test validation
    print("\n--- Test Validation Grammaticale ---")
    validation = validate_generated_program(program_str, tokenizer)
    
    print(f"✓ Programme valide: {validation['is_valid']}")
    print(f"✓ Erreur: {validation['error']}")
    print(f"✓ Suggestions: {len(validation['suggestions'])}")
    
    # Test amélioration
    print("\n--- Test Amélioration de Programme ---")
    improvement = model.validate_and_improve_program(generated_tokens, tokenizer)
    
    print(f"✓ Programme original: {len(improvement['original'])} caractères")
    print(f"✓ Programme corrigé: {len(improvement['corrected'])} caractères")
    print(f"✓ Améliorations: {len(improvement['validation']['suggestions'])}")
    
    return True

def test_training_step():
    """Test d'une étape d'entraînement"""
    print("\n=== Test Étape d'Entraînement ===")
    
    cfg = Config()
    tokenizer = GrammarTokenizer()
    
    # Modèle plus petit
    model = ImprovedHRM(
        model_dim=64,
        n_heads=2,
        grammar_vocab_size=len(tokenizer.vocab),
        N_cycles=2,
        T_steps=2
    )
    
    # Optimiseur
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
    loss_fn = torch.nn.CrossEntropyLoss(ignore_index=tokenizer.vocab['<pad>'])
    
    # Données
    batch_size = 2
    dummy_grid = torch.randint(0, 10, (batch_size, 8, 8))
    dummy_program = torch.randint(1, len(tokenizer.vocab)-1, (batch_size, 30))
    
    print(f"✓ Setup entraînement: batch_size={batch_size}")
    
    # Forward pass
    model.train()
    optimizer.zero_grad()
    
    segments, q_values, _ = model.forward_with_deep_supervision(
        dummy_grid, dummy_program, max_segments=2
    )
    
    # Calcul de la loss
    total_loss = 0
    for segment_logits in segments:
        logits = segment_logits[:, :-1, :]
        targets = dummy_program[:, 1:]
        loss = loss_fn(logits.reshape(-1, logits.size(-1)), targets.reshape(-1))
        total_loss += loss
    
    print(f"✓ Loss calculée: {total_loss.item():.4f}")
    
    # Backward pass
    total_loss.backward()
    
    # Vérifier les gradients
    grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
    print(f"✓ Gradient norm: {grad_norm:.4f}")
    
    optimizer.step()
    print(f"✓ Étape d'optimisation réussie")
    
    return True

def test_grammar_validation():
    """Test approfondi de la validation grammaticale"""
    print("\n=== Test Validation Grammaticale ===")
    
    tokenizer = GrammarTokenizer()
    
    # Programmes de test
    test_programs = [
        "INIT 10x10; EDIT 5 [0,0]; FILL 3 [1,1 2,2]; END",  # Valide
        "INIT 10x10; EDIT 5 [,0]; FILL 3 [1,1]; END",       # Coord malformée
        "INIT 10x10; <unk> 5 [0,0]; END",                   # Token inconnu
        "EDIT 5 [0,0]; FILL 3 [1,1]",                       # Pas d'INIT ni END
        "TRANSFERT {INIT 5x5; EDIT 7 [0,0]}; END"           # Avec TRANSFERT
    ]
    
    for i, program in enumerate(test_programs):
        print(f"\nTest {i+1}: {program[:50]}...")
        validation = validate_generated_program(program, tokenizer)
        
        print(f"  ✓ Valide: {validation['is_valid']}")
        if validation['error']:
            print(f"  ✗ Erreur: {validation['error'][:100]}...")
        
        stats = validation['statistics']
        print(f"  ✓ Stats: {stats['total_commands']} commandes, INIT: {stats['has_init']}, END: {stats['has_end']}")
        
        if validation['suggestions']:
            print(f"  💡 Suggestions: {len(validation['suggestions'])}")
    
    return True

def main():
    """Lance tous les tests"""
    print("🚀 TESTS HRM AMÉLIORÉ")
    print("=" * 50)
    
    try:
        # Test des composants
        if test_model_components():
            print("✅ Test composants: RÉUSSI")
        
        # Test entraînement
        if test_training_step():
            print("✅ Test entraînement: RÉUSSI")
        
        # Test validation
        if test_grammar_validation():
            print("✅ Test validation: RÉUSSI")
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS")
        print("Le HRM amélioré est prêt pour l'entraînement !")
        
    except Exception as e:
        print(f"\n❌ ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()