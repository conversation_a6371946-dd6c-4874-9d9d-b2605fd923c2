import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class HRM_Attention(nn.Module):
    """Module d'attention inspiré de HRM pour la compréhension hiérarchique"""
    def __init__(self, dim, num_heads=8):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # Projections comme dans HRM
        self.to_q = nn.Linear(dim, dim, bias=False)
        self.to_kv = nn.Linear(dim, dim * 2, bias=False)
        
        # Mécanisme de regroupement spatial
        self.pool = nn.AdaptiveAvgPool2d((1, 1))
        
        # Fusion des caractéristiques
        self.fusion = nn.Sequential(
            nn.Linear(dim * 2, dim),
            nn.GELU()
        )

    def forward(self, x):
        # x: [batch, H, W, D]
        batch, height, width, _ = x.shape
        
        # Créer une représentation globale (comme dans HRM)
        global_feat = self.pool(x.permute(0, 3, 1, 2)).view(batch, -1)
        
        # Projections des requêtes, clés et valeurs
        q = self.to_q(x).view(batch, height * width, self.num_heads, self.head_dim)
        kv = self.to_kv(x).view(batch, height * width, 2, self.num_heads, self.head_dim)
        k, v = kv.unbind(2)
        
        # Attention avec scaling HRM
        attn = torch.einsum('bqhd,bkhd->bhqk', q, k) * self.scale
        attn = F.softmax(attn, dim=-1)
        
        # Agrégation
        out = torch.einsum('bhqk,bkhd->bqhd', attn, v)
        out = out.contiguous().view(batch, height, width, -1)
        
        # Fusion avec la représentation globale
        global_expanded = global_feat.unsqueeze(1).unsqueeze(1).expand_as(out)
        out = self.fusion(torch.cat([out, global_expanded], dim=-1))
        
        return out

class HierarchicalPatternModule(nn.Module):
    """Module hiérarchique pour la détection de motifs à différentes échelles"""
    def __init__(self, dim, levels=3):
        super().__init__()
        self.levels = levels
        self.downsample = nn.ModuleList()
        self.processors = nn.ModuleList()
        
        # Créer les niveaux hiérarchiques
        for i in range(levels):
            if i > 0:
                self.downsample.append(nn.AvgPool2d(2, 2))
            self.processors.append(nn.Sequential(
                nn.Conv2d(dim, dim, 3, padding=1),
                nn.GELU(),
                nn.Conv2d(dim, dim, 3, padding=1)
            ))
        
        self.upsample = nn.ModuleList()
        for i in range(levels-1):
            self.upsample.append(nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True))
        
        self.fusion = nn.Conv2d(dim * levels, dim, 1)

    def forward(self, x):
        # x: [batch, D, H, W]
        features = []
        
        # Traitement à différentes échelles
        current = x
        for i in range(self.levels):
            if i > 0:
                current = self.downsample[i-1](current)
            processed = self.processors[i](current)
            features.append(processed)
        
        # Fusion multi-échelle
        upscaled = []
        for i in range(self.levels):
            if i < self.levels - 1:
                feat = self.upsample[i](features[i])
            else:
                feat = features[i]
            upscaled.append(feat)
        
        # Concaténation et fusion finale
        fused = self.fusion(torch.cat(upscaled, dim=1))
        return fused

class HRM_GridDINO(nn.Module):
    """Modèle hybride intégrant HRM dans GridDINO"""
    def __init__(self, 
                 grid_size=30, 
                 vocab_size=10, 
                 embed_dim=128, 
                 num_layers=6, 
                 num_heads=8,
                 pattern_levels=3):
        super().__init__()
        self.grid_size = grid_size
        
        # Embeddings
        self.value_embed = ValueEmbedding(vocab_size, embed_dim)
        self.pos_enc = PositionalEncoding2D(embed_dim, grid_size)
        
        # Module HRM d'attention
        self.hrm_attn = HRM_Attention(embed_dim, num_heads)
        
        # Blocs de traitement hiérarchique
        self.hierarchical = nn.ModuleList([
            HierarchicalPatternModule(embed_dim, pattern_levels)
            for _ in range(num_layers)
        ])
        
        # Tête de reconstruction
        self.recon_head = nn.Sequential(
            nn.LayerNorm(embed_dim),
            nn.Linear(embed_dim, vocab_size)
        
        # Tête de prédiction d'actions
        self.action_head = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(),
            nn.Linear(embed_dim, 128),
            nn.GELU(),
            nn.Linear(128, 20)  # 20 types d'actions
        )

    def forward(self, grid, action_sequence=None):
        # Embedding de base
        x = self.value_embed(grid)
        x = self.pos_enc(x)
        
        # Attention HRM
        x = self.hrm_attn(x)
        
        # Traitement hiérarchique
        x_perm = x.permute(0, 3, 1, 2)  # [batch, D, H, W]
        for layer in self.hierarchical:
            x_perm = layer(x_perm)
        
        # Reconstruction
        x_recon = x_perm.permute(0, 2, 3, 1)  # [batch, H, W, D]
        recon_output = self.recon_head(x_recon)
        
        # Prédiction d'actions
        action_pred = self.action_head(x_perm)
        
        # Si une séquence d'actions est fournie, prédire la grille transformée
        if action_sequence is not None:
            # Intégration des actions dans la représentation
            action_emb = self.action_embed(action_sequence)
            action_emb = action_emb.unsqueeze(-1).unsqueeze(-1).expand_as(x_perm)
            fused = torch.cat([x_perm, action_emb], dim=1)
            
            # Transformation via convolution pointwise
            transform = self.transform_conv(fused)
            return transform, recon_output, action_pred
        
        return recon_output, action_pred

# Modules complémentaires (inchangés)
class ValueEmbedding(nn.Module):
    # (Identique à l'implémentation précédente)

class PositionalEncoding2D(nn.Module):
    # (Identique à l'implémentation précédente)