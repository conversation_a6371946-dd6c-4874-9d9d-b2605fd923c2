# Requirements Document

## Introduction

Développer un système à deux étages DINO-HRM pour résoudre les puzzles ARC en reproduisant le processus cognitif humain : analyse comparative des exemples d'entraînement, catégorisation automatique, génération de scénarios généralisés, puis résolution de paramètres et validation.

## Requirements

### Requirement 1

**User Story:** En tant que développeur, je veux un dataset multi-exemples pour que DINO puisse analyser tous les exemples train d'un puzzle simultanément.

#### Acceptance Criteria

1. WHEN le dataset charge un puzzle THEN il SHALL retourner tous les exemples train (pas seulement le premier)
2. WHEN les grilles ont des tailles différentes THEN le système SHALL les normaliser à 30x30 avec padding
3. WHEN un puzzle a N exemples train THEN le dataset SHALL fournir N paires (input, output)

### Requirement 2

**User Story:** En tant que système IA, je veux un modèle DINO-ARC pour analyser les patterns multi-exemples et catégoriser automatiquement les puzzles.

#### Acceptance Criteria

1. WHEN DINO reçoit plusieurs exemples train THEN il SHALL détecter les patterns communs
2. WHEN les patterns sont détectés THEN DINO SHALL catégoriser le puzzle automatiquement
3. WHEN la catégorisation est faite THEN DINO SHALL générer un scénario généralisé avec paramètres variables

### Requirement 3

**User Story:** En tant que générateur de scénarios, je veux transformer les commandes AGI spécifiques en templates généralisés.

#### Acceptance Criteria

1. WHEN une commande AGI contient des couleurs spécifiques THEN le généralisateur SHALL les remplacer par des variables
2. WHEN une commande contient des coordonnées THEN le généralisateur SHALL les remplacer par des patterns géométriques
3. WHEN le template est créé THEN il SHALL inclure les contraintes de résolution des paramètres

### Requirement 4

**User Story:** En tant que résolveur HRM, je veux instancier les paramètres d'un scénario généralisé pour un test_input spécifique.

#### Acceptance Criteria

1. WHEN HRM reçoit un scénario généralisé THEN il SHALL résoudre tous les paramètres variables
2. WHEN les paramètres sont résolus THEN HRM SHALL exécuter les commandes sur test_input
3. WHEN l'exécution est terminée THEN HRM SHALL valider le résultat sur les exemples train

### Requirement 5

**User Story:** En tant que système de validation, je veux vérifier que la solution générée est cohérente avec tous les exemples train.

#### Acceptance Criteria

1. WHEN une solution est générée THEN le système SHALL l'appliquer à tous les train_inputs
2. WHEN les résultats sont calculés THEN ils SHALL correspondre aux train_outputs attendus
3. IF la validation échoue THEN le système SHALL ajuster les paramètres et réessayer

### Requirement 6

**User Story:** En tant que pipeline complet, je veux orchestrer DINO et HRM pour résoudre un puzzle ARC de bout en bout.

#### Acceptance Criteria

1. WHEN un puzzle JSON est fourni THEN DINO SHALL l'analyser et proposer une catégorie
2. WHEN la catégorie est déterminée THEN le système SHALL sélectionner le template de scénario approprié
3. WHEN HRM résout les paramètres THEN le système SHALL retourner la solution finale pour test_input

### Requirement 7

**User Story:** En tant que système d'entraînement, je veux entraîner DINO sur la catégorisation et HRM sur la résolution de paramètres.

#### Acceptance Criteria

1. WHEN DINO s'entraîne THEN il SHALL utiliser l'apprentissage contrastif avec augmentations (rotations, permutations couleurs)
2. WHEN HRM s'entraîne THEN il SHALL apprendre à résoudre les paramètres à partir de scénarios généralisés
3. WHEN l'entraînement est terminé THEN les deux modèles SHALL être intégrés dans un pipeline fonctionnel