class GrammarConstrainedHRM(GridToProgramHRM):
    def __init__(self, *args, grammar_validator, **kwargs):
        super().__init__(*args, **kwargs)
        self.grammar_validator = grammar_validator
        self.tokenizer = GrammarAwareTokenizer(grammar_validator)
        
        # Couche d'alignement grammatical
        self.grammar_layer = nn.Linear(
            self.model_dim, 
            len(self.tokenizer.vocab)
        )
        
    def forward(self, grid, program_tokens=None, max_segments=5):
        # Encodage standard
        grid_emb = self._encode_grid(grid)
        
        # Génération avec contraintes grammaticales
        segments = []
        for seg in range(max_segments):
            hrm_state = self.hierarchical_reasoning(grid_emb)
            
            # Projection dans l'espace grammatical
            grammar_logits = self.grammar_layer(hrm_state)
            
            # Décodage contraint
            if program_tokens is not None:
                seg_tokens = program_tokens[:, seg]
                logits = self._decode_with_constraints(hrm_state, seg_tokens)
                segments.append(logits)
            
            # Validation en temps réel
            if not self.training:
                current_program = self.tokenizer.decode(segments)
                is_valid, _ = self.grammar_validator.validate_program(current_program)
                if not is_valid:
                    break
        
        return segments

    def _decode_with_constraints(self, hrm_state, tokens):
        """Décodage avec contraintes grammaticales"""
        # 1. Génération libre initiale
        free_logits = super().forward(hrm_state, tokens)
        
        # 2. Masquage des tokens invalides
        current_program = self.tokenizer.decode(tokens)
        next_valid_tokens = self._get_valid_next_tokens(current_program)
        mask = torch.full_like(free_logits, float('-inf'))
        mask[:, :, next_valid_tokens] = 0
        
        return free_logits + mask

    def _get_valid_next_tokens(self, program):
        """Retourne les tokens valides suivant l'état actuel"""
        try:
            partial_tree = self.grammar_validator.parser.parse(program, start='start')
            return {self.tokenizer.vocab[t] for t in partial_tree.expected_tokens}
        except LarkError:
            return set(self.tokenizer.vocab.values())