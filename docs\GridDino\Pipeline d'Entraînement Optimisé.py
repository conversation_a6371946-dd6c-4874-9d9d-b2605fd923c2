def train_grammar_constrained(model, dataloader):
    optimizer = torch.optim.AdamW(model.parameters(), lr=5e-5)
    grammar_val = AGIGrammarValidator()
    
    for epoch in range(10):
        for grid, program in dataloader:
            # Tokenisation grammaticale
            tokens = [model.tokenizer.tokenize(p) for p in program]
            tokens = pad_sequence(tokens, batch_first=True)
            
            # Forward pass avec contraintes
            logits = model(grid, tokens)
            
            # Calcul de perte adaptatif
            loss = 0
            for seg_logits in logits:
                # Poids plus élevé pour les tokens structurants
                weights = torch.ones_like(tokens)
                weights[tokens.isin([model.tokenizer.vocab[t] for t in ['INIT', ';', '}']])] = 2.0
                loss += F.cross_entropy(
                    seg_logits.view(-1, seg_logits.size(-1)),
                    tokens.view(-1),
                    weight=weights
                )
            
            # Backpropagation
            loss.backward()
            optimizer.step()
            optimizer.zero_grad()
            
            # Validation grammaticale
            with torch.no_grad():
                generated = model(grid)
                validity = [grammar_val.validate_program(p)[0] for p in generated]
                accuracy = sum(validity) / len(validity)