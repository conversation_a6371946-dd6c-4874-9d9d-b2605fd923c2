def evaluate_grammar_compliance(model, dataset):
    results = []
    for grid, _ in dataset:
        program = model.generate(grid)
        is_valid, error = grammar_val.validate_program(program)
        stats = grammar_val.get_command_statistics(program)
        results.append({
            "valid": is_valid,
            "error": error,
            "stats": stats,
            "program": program
        })
    
    validity_rate = sum(r['valid'] for r in results) / len(results)
    avg_commands = sum(r['stats']['total_commands'] for r in results) / len(results)
    
    print(f"Validité grammaticale: {validity_rate:.2%}")
    print(f"Commandes moyennes par programme: {avg_commands:.1f}")
    return results