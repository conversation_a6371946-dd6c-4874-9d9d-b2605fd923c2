"""
Résolveur de paramètres HRM pour DINO-HRM-ARC.

Instancie les paramètres variables d'un scénario généralisé pour un test_input spécifique.
Utilise l'analyse des exemples train pour résoudre les contraintes et générer les commandes concrètes.
"""

import torch
import torch.nn as nn
import numpy as np
import re
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import Counter
from dataclasses import dataclass

from src.scenario_generalizer import GeneralizedScenario, ParameterConstraint, ParameterType
from src.command_executor import CommandExecutor


@dataclass
class ResolvedParameter:
    """Paramètre résolu avec sa valeur concrète."""
    param_name: str
    param_type: ParameterType
    resolved_value: Any
    confidence: float = 1.0


class ParameterAnalyzer:
    """
    Analyseur pour extraire des informations des grilles ARC.
    
    Fournit les fonctions d'analyse nécessaires pour résoudre
    les contraintes des paramètres.
    """
    
    @staticmethod
    def get_most_frequent_color(grid: np.ndarray, exclude_background: bool = True) -> int:
        """Retourne la couleur la plus fréquente (excluant le fond si demandé)."""
        flat_grid = grid.flatten()
        color_counts = Counter(flat_grid)
        
        if exclude_background and 0 in color_counts:
            del color_counts[0]
        
        if not color_counts:
            return 0
        
        return color_counts.most_common(1)[0][0]
    
    @staticmethod
    def get_dominant_color_output(train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> int:
        """Retourne la couleur dominante dans les outputs des exemples train."""
        all_output_colors = []
        
        for _, output_grid in train_pairs:
            colors = output_grid.flatten()
            all_output_colors.extend(colors[colors != 0])  # Exclure le fond
        
        if not all_output_colors:
            return 1  # Couleur par défaut
        
        return Counter(all_output_colors).most_common(1)[0][0]
    
    @staticmethod
    def detect_geometric_center(grid: np.ndarray) -> Tuple[int, int]:
        """Détecte le centre géométrique de la grille."""
        h, w = grid.shape
        return (h // 2, w // 2)
    
    @staticmethod
    def detect_main_object(grid: np.ndarray) -> List[Tuple[int, int]]:
        """Détecte l'objet principal (plus grande composante connexe non-fond)."""
        # Trouver toutes les positions non-fond
        non_zero_positions = np.argwhere(grid != 0)
        
        if len(non_zero_positions) == 0:
            return []
        
        # Pour simplifier, retourner toutes les positions non-fond
        # Dans une implémentation complète, on ferait une analyse de composantes connexes
        return [(int(pos[0]), int(pos[1])) for pos in non_zero_positions]
    
    @staticmethod
    def detect_pattern_positions(input_grid: np.ndarray, output_grid: np.ndarray) -> List[Tuple[int, int]]:
        """Détecte les positions où des changements ont eu lieu."""
        diff_mask = input_grid != output_grid
        diff_positions = np.argwhere(diff_mask)
        return [(int(pos[0]), int(pos[1])) for pos in diff_positions]
    
    @staticmethod
    def detect_symmetry_axis(grid: np.ndarray) -> str:
        """Détecte l'axe de symétrie principal."""
        h, w = grid.shape
        
        # Test symétrie horizontale
        if np.array_equal(grid, np.flip(grid, axis=1)):
            return "VERTICAL"
        
        # Test symétrie verticale
        if np.array_equal(grid, np.flip(grid, axis=0)):
            return "HORIZONTAL"
        
        return "VERTICAL"  # Par défaut
    
    @staticmethod
    def detect_rotation_angle(input_grid: np.ndarray, output_grid: np.ndarray) -> int:
        """Détecte l'angle de rotation entre input et output."""
        # Test rotations de 90, 180, 270 degrés
        for angle in [90, 180, 270]:
            rotated = np.rot90(input_grid, k=angle//90)
            if np.array_equal(rotated, output_grid):
                return angle
        
        return 0  # Pas de rotation détectée
    
    @staticmethod
    def detect_scale_factor(input_grid: np.ndarray, output_grid: np.ndarray) -> int:
        """Détecte le facteur d'échelle entre input et output."""
        input_objects = len(np.argwhere(input_grid != 0))
        output_objects = len(np.argwhere(output_grid != 0))
        
        if input_objects == 0:
            return 1
        
        scale = output_objects // input_objects
        return max(1, scale)
    
    @staticmethod
    def detect_translation_vector(input_grid: np.ndarray, output_grid: np.ndarray) -> Tuple[int, int]:
        """Détecte le vecteur de translation."""
        # Simplification : détecter le décalage du centre de masse
        input_positions = np.argwhere(input_grid != 0)
        output_positions = np.argwhere(output_grid != 0)
        
        if len(input_positions) == 0 or len(output_positions) == 0:
            return (0, 0)
        
        input_center = np.mean(input_positions, axis=0)
        output_center = np.mean(output_positions, axis=0)
        
        translation = output_center - input_center
        return (int(translation[0]), int(translation[1]))


class HRMParameterResolver:
    """
    Résolveur de paramètres HRM.
    
    Instancie les paramètres variables d'un scénario généralisé
    en analysant les exemples train et le test_input.
    """
    
    def __init__(self):
        """Initialise le résolveur avec l'exécuteur de commandes."""
        self.analyzer = ParameterAnalyzer()
        self.executor = CommandExecutor()
        
    def resolve_parameters(self, 
                          scenario: GeneralizedScenario,
                          test_input: np.ndarray,
                          train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> Dict[str, ResolvedParameter]:
        """
        Résout tous les paramètres d'un scénario généralisé.
        
        Args:
            scenario: Scénario avec template et contraintes
            test_input: Grille de test à transformer
            train_pairs: Exemples d'entraînement pour l'analyse
        
        Returns:
            Dict des paramètres résolus
        """
        resolved_params = {}
        
        for constraint in scenario.constraints:
            resolved_value = self._resolve_single_parameter(
                constraint, test_input, train_pairs
            )
            
            resolved_params[constraint.param_name] = ResolvedParameter(
                param_name=constraint.param_name,
                param_type=constraint.param_type,
                resolved_value=resolved_value,
                confidence=1.0  # TODO: calculer la confiance réelle
            )
        
        return resolved_params
    
    def _resolve_single_parameter(self,
                                 constraint: ParameterConstraint,
                                 test_input: np.ndarray,
                                 train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> Any:
        """
        Résout un paramètre individuel selon sa contrainte.
        
        Args:
            constraint: Contrainte du paramètre
            test_input: Grille de test
            train_pairs: Exemples d'entraînement
        
        Returns:
            Valeur résolue du paramètre
        """
        constraint_type = constraint.constraint_type
        
        # Résolution des contraintes de couleur
        if constraint.param_type == ParameterType.COLOR:
            return self._resolve_color_constraint(constraint_type, test_input, train_pairs)
        
        # Résolution des contraintes de pattern
        elif constraint.param_type == ParameterType.PATTERN:
            return self._resolve_pattern_constraint(constraint_type, test_input, train_pairs)
        
        # Résolution des contraintes de position
        elif constraint.param_type == ParameterType.POSITION:
            return self._resolve_position_constraint(constraint_type, test_input, train_pairs)
        
        # Résolution des contraintes de direction
        elif constraint.param_type == ParameterType.DIRECTION:
            return self._resolve_direction_constraint(constraint_type, test_input, train_pairs)
        
        # Résolution des contraintes de taille
        elif constraint.param_type == ParameterType.SIZE:
            return self._resolve_size_constraint(constraint_type, test_input, train_pairs)
        
        # Résolution des contraintes de comptage
        elif constraint.param_type == ParameterType.COUNT:
            return self._resolve_count_constraint(constraint_type, test_input, train_pairs)
        
        # Contrainte spécifique (valeur fixe)
        elif constraint_type == "specific_color":
            return constraint.constraint_data
        
        elif constraint_type == "specific_size":
            return f"{constraint.constraint_data[0]}x{constraint.constraint_data[1]}"
        
        elif constraint_type == "coordinate_list":
            coords = constraint.constraint_data
            return "(" + " ".join([f"[{r},{c}]" for r, c in coords]) + ")"
        
        # Valeur par défaut
        return "0"
    
    def _resolve_color_constraint(self, constraint_type: str, test_input: np.ndarray, 
                                train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """Résout les contraintes de couleur."""
        if constraint_type == "most_frequent_output":
            color = self.analyzer.get_dominant_color_output(train_pairs)
            return str(color)
        
        elif constraint_type == "dominant_color":
            color = self.analyzer.get_most_frequent_color(test_input)
            return str(color)
        
        elif constraint_type == "most_frequent":
            color = self.analyzer.get_most_frequent_color(test_input)
            return str(color)
        
        elif constraint_type in ["line_color_rule", "fill_rule", "boundary_marker"]:
            # Analyser les exemples pour déduire la règle de couleur
            color = self.analyzer.get_dominant_color_output(train_pairs)
            return str(color)
        
        return "1"  # Couleur par défaut
    
    def _resolve_pattern_constraint(self, constraint_type: str, test_input: np.ndarray,
                                  train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """Résout les contraintes de pattern."""
        if constraint_type == "input_pattern_match":
            # Détecter les positions qui changent dans les exemples
            all_change_positions = []
            for input_grid, output_grid in train_pairs:
                positions = self.analyzer.detect_pattern_positions(input_grid, output_grid)
                all_change_positions.extend(positions)
            
            # Appliquer le même pattern au test_input
            if all_change_positions:
                # Prendre les positions relatives les plus communes
                unique_positions = list(set(all_change_positions))[:10]  # Limiter
                return "(" + " ".join([f"[{r},{c}]" for r, c in unique_positions]) + ")"
        
        elif constraint_type == "main_object":
            positions = self.analyzer.detect_main_object(test_input)
            if positions:
                return "(" + " ".join([f"[{r},{c}]" for r, c in positions[:10]]) + ")"
        
        elif constraint_type == "full_grid":
            h, w = test_input.shape
            return f"[0,0 {h-1},{w-1}]"
        
        elif constraint_type in ["rectangle_detection", "completion_area"]:
            # Détecter les rectangles ou zones à compléter
            positions = self.analyzer.detect_main_object(test_input)
            if positions:
                return "(" + " ".join([f"[{r},{c}]" for r, c in positions[:5]]) + ")"
        
        return "[0,0]"  # Position par défaut
    
    def _resolve_position_constraint(self, constraint_type: str, test_input: np.ndarray,
                                   train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """Résout les contraintes de position."""
        if constraint_type == "geometric_center":
            r, c = self.analyzer.detect_geometric_center(test_input)
            return f"[{r},{c}]"
        
        elif constraint_type == "center_placement":
            r, c = self.analyzer.detect_geometric_center(test_input)
            return f"[{r},{c}]"
        
        elif constraint_type == "translation_vector":
            if train_pairs:
                dr, dc = self.analyzer.detect_translation_vector(
                    train_pairs[0][0], train_pairs[0][1]
                )
                # Appliquer la translation au centre du test_input
                center_r, center_c = self.analyzer.detect_geometric_center(test_input)
                return f"[{center_r + dr},{center_c + dc}]"
        
        return "[0,0]"
    
    def _resolve_direction_constraint(self, constraint_type: str, test_input: np.ndarray,
                                    train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """Résout les contraintes de direction."""
        if constraint_type == "symmetry_axis":
            axis = self.analyzer.detect_symmetry_axis(test_input)
            return axis
        
        elif constraint_type == "rotation_detection":
            if train_pairs:
                angle = self.analyzer.detect_rotation_angle(
                    train_pairs[0][0], train_pairs[0][1]
                )
                return str(angle)
        
        elif constraint_type == "repetition_axis":
            return "HORIZONTAL"  # Par défaut
        
        return "HORIZONTAL"
    
    def _resolve_size_constraint(self, constraint_type: str, test_input: np.ndarray,
                               train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """Résout les contraintes de taille."""
        if constraint_type == "output_size":
            if train_pairs:
                output_shape = train_pairs[0][1].shape
                return f"{output_shape[0]}x{output_shape[1]}"
        
        h, w = test_input.shape
        return f"{h}x{w}"
    
    def _resolve_count_constraint(self, constraint_type: str, test_input: np.ndarray,
                                train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """Résout les contraintes de comptage."""
        if constraint_type == "pattern_frequency":
            # Compter les objets dans test_input
            objects = len(self.analyzer.detect_main_object(test_input))
            return str(max(1, objects // 10))  # Approximation
        
        elif constraint_type == "size_ratio":
            if train_pairs:
                scale = self.analyzer.detect_scale_factor(
                    train_pairs[0][0], train_pairs[0][1]
                )
                return str(scale)
        
        return "1"
    
    def instantiate_template(self, template: str, resolved_params: Dict[str, ResolvedParameter]) -> str:
        """
        Instancie un template avec les paramètres résolus.
        
        Args:
            template: Template avec variables {var_name}
            resolved_params: Paramètres résolus
        
        Returns:
            Commande AGI concrète
        """
        concrete_command = template
        
        for param_name, resolved_param in resolved_params.items():
            placeholder = f"{{{param_name}}}"
            concrete_command = concrete_command.replace(placeholder, str(resolved_param.resolved_value))
        
        return concrete_command
    
    def execute_and_validate(self, 
                           concrete_command: str,
                           test_input: np.ndarray,
                           train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> Tuple[Optional[np.ndarray], bool]:
        """
        Exécute une commande concrète et valide le résultat.
        
        Args:
            concrete_command: Commande AGI à exécuter
            test_input: Grille de test
            train_pairs: Exemples pour validation
        
        Returns:
            Tuple (résultat, validation_réussie)
        """
        try:
            # Préparer les commandes (initialiser la grille + commande)
            h, w = test_input.shape
            commands = [f"INIT {w} {h}"] + [concrete_command]
            
            # Initialiser l'exécuteur avec la grille de test
            if isinstance(test_input, torch.Tensor):
                self.executor.grid = test_input.detach().numpy()
            else:
                self.executor.grid = test_input.copy()
            self.executor.width = w
            self.executor.height = h
            
            # Exécuter la commande
            result_dict = self.executor.execute_commands(commands)
            
            if result_dict["success"] and result_dict["grid"] is not None:
                result = np.array(result_dict["grid"])
                
                # Valider en appliquant la même commande aux exemples train
                validation_success = self._validate_on_train_examples(
                    concrete_command, train_pairs
                )
                
                return result, validation_success
            else:
                print(f"Erreur d'exécution: {result_dict.get('error', 'Erreur inconnue')}")
                return None, False
            
        except Exception as e:
            print(f"Erreur d'exécution: {e}")
            return None, False
    
    def _validate_on_train_examples(self, 
                                  concrete_command: str,
                                  train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> bool:
        """
        Valide une commande sur les exemples d'entraînement.
        
        Args:
            concrete_command: Commande à valider
            train_pairs: Exemples (input, expected_output)
        
        Returns:
            True si la validation réussit sur au moins 50% des exemples
        """
        successful_validations = 0
        
        for input_grid, expected_output in train_pairs:
            try:
                # Préparer les commandes pour cet exemple
                h, w = input_grid.shape
                commands = [f"INIT {w} {h}"] + [concrete_command]
                
                # Créer un nouvel exécuteur pour éviter les interférences
                temp_executor = CommandExecutor()
                if isinstance(input_grid, torch.Tensor):
                    temp_executor.grid = input_grid.detach().numpy()
                else:
                    temp_executor.grid = input_grid.copy()
                temp_executor.width = w
                temp_executor.height = h
                
                # Exécuter
                result_dict = temp_executor.execute_commands(commands)
                
                if (result_dict["success"] and 
                    result_dict["grid"] is not None and
                    np.array_equal(np.array(result_dict["grid"]), expected_output)):
                    successful_validations += 1
                    
            except Exception:
                continue  # Échec de validation pour cet exemple
        
        # Réussir si au moins 50% des exemples sont corrects
        success_rate = successful_validations / len(train_pairs) if train_pairs else 0
        return success_rate >= 0.5
    
    def resolve_and_execute(self, 
                          scenario: GeneralizedScenario,
                          test_input: np.ndarray,
                          train_pairs: List[Tuple[np.ndarray, np.ndarray]],
                          max_retries: int = 3) -> Optional[np.ndarray]:
        """
        Résout les paramètres et exécute le scénario complet.
        
        Args:
            scenario: Scénario généralisé
            test_input: Grille de test
            train_pairs: Exemples d'entraînement
            max_retries: Nombre maximum de tentatives
        
        Returns:
            Grille résultat ou None si échec
        """
        for attempt in range(max_retries):
            try:
                # Résoudre les paramètres
                resolved_params = self.resolve_parameters(scenario, test_input, train_pairs)
                
                # Instancier le template
                concrete_command = self.instantiate_template(scenario.template, resolved_params)
                
                print(f"Tentative {attempt + 1}: {concrete_command}")
                
                # Exécuter et valider
                result, validation_success = self.execute_and_validate(
                    concrete_command, test_input, train_pairs
                )
                
                if result is not None and validation_success:
                    print(f"✓ Succès à la tentative {attempt + 1}")
                    return result
                
                # Si échec, ajuster les paramètres pour la prochaine tentative
                if attempt < max_retries - 1:
                    self._adjust_parameters_for_retry(resolved_params, attempt)
                    
            except Exception as e:
                print(f"Erreur tentative {attempt + 1}: {e}")
                continue
        
        print(f"✗ Échec après {max_retries} tentatives")
        return None
    
    def _adjust_parameters_for_retry(self, resolved_params: Dict[str, ResolvedParameter], attempt: int):
        """
        Ajuste les paramètres pour une nouvelle tentative.
        
        Args:
            resolved_params: Paramètres à ajuster
            attempt: Numéro de la tentative (pour variation)
        """
        # Stratégies d'ajustement simples
        for param_name, param in resolved_params.items():
            if param.param_type == ParameterType.COLOR:
                # Essayer d'autres couleurs
                current_color = int(param.resolved_value) if param.resolved_value.isdigit() else 1
                new_color = (current_color + attempt + 1) % 10
                param.resolved_value = str(new_color)
            
            elif param.param_type == ParameterType.COUNT:
                # Ajuster les comptages
                current_count = int(param.resolved_value) if param.resolved_value.isdigit() else 1
                new_count = max(1, current_count + attempt)
                param.resolved_value = str(new_count)