# Charger le modèle pré-entraîné
model = GridDINO(grid_size=30, vocab_size=10)
model.load_state_dict(torch.load('griddino_pretrained.pth'))

# Geler les couches basses
for param in model.parameters():
    param.requires_grad = False
    
# Ajouter une tête de classification
class ActionClassifier(nn.Module):
    def __init__(self, input_dim, num_actions):
        super().__init__()
        self.pool = nn.AdaptiveAvgPool2d(1)
        self.classifier = nn.Linear(input_dim, num_actions)
    
    def forward(self, features):
        # features: [batch, H, W, D]
        x = features.permute(0, 3, 1, 2)  # [batch, D, H, W]
        x = self.pool(x).squeeze(-1).squeeze(-1)
        return self.classifier(x)

# Mod<PERSON><PERSON> complet
action_classifier = ActionClassifier(input_dim=128, num_actions=8)
full_model = nn.Sequential(model, action_classifier)

# Entraînement sur données annotées
# ...