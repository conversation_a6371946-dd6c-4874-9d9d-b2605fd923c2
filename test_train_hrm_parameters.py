#!/usr/bin/env python3
"""
Script de test pour l'entraînement HRM Parameter Resolution.

Valide que le système d'entraînement peut apprendre à résoudre
les paramètres des scénarios généralisés.
"""

import sys
import os
sys.path.append('.')

import torch
import numpy as np
from training.train_hrm_parameters import (
    HRMTrainingConfig, ParameterResolutionDataset, HRMParameterNet, 
    HRMParameterTrainer, create_hrm_datasets
)
from src.multi_example_dataset import MultiExampleDataset
from src.scenario_generalizer import ScenarioGeneralizer, GeneralizedScenario, ParameterConstraint, ParameterType
from models.hrm_parameter_resolver import ResolvedParameter


def create_mock_hrm_dataset():
    """Crée un dataset mock pour les tests HRM."""
    # Créer des données synthétiques
    mock_data = []
    
    for i in range(20):  # 20 puzzles synthétiques
        # Puzzle simple avec patterns variés
        train_pairs = [
            (torch.randint(0, 4, (6, 6)), torch.randint(0, 4, (6, 6))),
            (torch.randint(0, 4, (6, 6)), torch.randint(0, 4, (6, 6)))
        ]
        
        test_input = torch.randint(0, 4, (6, 6))
        
        mock_data.append({
            'train_pairs': train_pairs,
            'test_input': test_input,
            'puzzle_id': f'hrm_mock_puzzle_{i:03d}'
        })
    
    # Créer un dataset mock
    mock_dataset = MultiExampleDataset.__new__(MultiExampleDataset)
    mock_dataset.data = mock_data
    mock_dataset.max_grid_size = 30
    
    return mock_dataset


def test_hrm_training_config():
    """Test de la configuration d'entraînement HRM."""
    print("=== Test HRMTrainingConfig ===")
    
    # Configuration par défaut
    config = HRMTrainingConfig()
    
    print(f"Hidden dim: {config.hidden_dim}")
    print(f"Batch size: {config.batch_size}")
    print(f"Learning rate: {config.learning_rate}")
    print(f"Device: {config.device}")
    
    # Configuration personnalisée
    custom_config = HRMTrainingConfig(
        hidden_dim=64,
        batch_size=4,
        num_epochs=10,
        device="cpu"
    )
    
    assert custom_config.hidden_dim == 64, "Hidden dim personnalisé incorrect"
    assert custom_config.batch_size == 4, "Batch size personnalisé incorrect"
    assert custom_config.device == "cpu", "Device personnalisé incorrect"
    
    print("✓ HRMTrainingConfig OK")


def test_parameter_resolution_dataset():
    """Test du dataset de résolution de paramètres."""
    print("\n=== Test ParameterResolutionDataset ===")
    
    # Configuration de test
    config = HRMTrainingConfig(batch_size=2, device="cpu")
    
    # Dataset mock
    base_dataset = create_mock_hrm_dataset()
    
    # Dataset de résolution de paramètres
    param_dataset = ParameterResolutionDataset(base_dataset, config, mode="train")
    
    print(f"Nombre d'exemples générés: {len(param_dataset)}")
    
    if len(param_dataset) > 0:
        # Test d'un échantillon
        sample = param_dataset[0]
        
        print(f"Clés de l'échantillon: {list(sample.keys())}")
        print(f"Scenario features shape: {sample['scenario_features'].shape}")
        print(f"Grid features shape: {sample['grid_features'].shape}")
        print(f"Param targets shape: {sample['param_targets'].shape}")
        print(f"Category: {sample['category']}")
        print(f"Puzzle ID: {sample['puzzle_id']}")
        
        # Vérifications
        assert 'scenario_features' in sample, "Doit contenir scenario_features"
        assert 'grid_features' in sample, "Doit contenir grid_features"
        assert 'param_targets' in sample, "Doit contenir param_targets"
        assert sample['scenario_features'].shape[0] == 3, "Scenario features doit avoir 3 dimensions"
        assert sample['grid_features'].shape[0] == 6, "Grid features doit avoir 6 dimensions"
        assert sample['param_targets'].shape[0] == 10, "Param targets doit avoir 10 dimensions"
        
        print("✓ ParameterResolutionDataset OK")
    else:
        print("⚠ Aucun exemple généré (normal avec données mock)")


def test_hrm_parameter_net():
    """Test du réseau de neurones HRM."""
    print("\n=== Test HRMParameterNet ===")
    
    config = HRMTrainingConfig(hidden_dim=32, device="cpu")
    
    # Créer le modèle
    model = HRMParameterNet(config)
    
    print(f"Modèle: {type(model).__name__}")
    print(f"Paramètres: {sum(p.numel() for p in model.parameters())}")
    
    # Test forward pass
    batch_size = 4
    scenario_features = torch.randn(batch_size, 3)
    grid_features = torch.randn(batch_size, 6)
    
    # Forward pass
    with torch.no_grad():
        output = model(scenario_features, grid_features)
    
    print(f"Input scenario shape: {scenario_features.shape}")
    print(f"Input grid shape: {grid_features.shape}")
    print(f"Output shape: {output.shape}")
    
    # Vérifications
    assert output.shape == (batch_size, 10), f"Output shape incorrect: {output.shape}"
    assert not torch.isnan(output).any(), "Output ne doit pas contenir de NaN"
    
    print("✓ HRMParameterNet OK")


def test_hrm_trainer_creation():
    """Test de création de l'entraîneur HRM."""
    print("\n=== Test HRMParameterTrainer ===")
    
    config = HRMTrainingConfig(
        hidden_dim=32,
        batch_size=2,
        device="cpu"
    )
    
    trainer = HRMParameterTrainer(config)
    
    print(f"Device: {trainer.device}")
    print(f"Modèle: {type(trainer.model).__name__}")
    print(f"Optimiseur: {type(trainer.optimizer).__name__}")
    print(f"Scheduler: {type(trainer.scheduler).__name__}")
    print(f"Critère: {type(trainer.criterion).__name__}")
    
    # Vérifier que le modèle est sur le bon device
    model_device = next(trainer.model.parameters()).device
    assert model_device.type == trainer.device.type, f"Device types doivent correspondre"
    
    # Vérifier les composants
    assert hasattr(trainer, 'criterion'), "Doit avoir un critère de perte"
    assert hasattr(trainer, 'train_history'), "Doit avoir un historique d'entraînement"
    
    print("✓ HRMParameterTrainer OK")


def test_hrm_forward_pass():
    """Test d'un forward pass complet HRM."""
    print("\n=== Test forward pass HRM ===")
    
    config = HRMTrainingConfig(hidden_dim=32, batch_size=2, device="cpu")
    
    trainer = HRMParameterTrainer(config)
    base_dataset = create_mock_hrm_dataset()
    param_dataset = ParameterResolutionDataset(base_dataset, config, mode="train")
    
    if len(param_dataset) == 0:
        print("⚠ Pas d'exemples générés, test ignoré")
        return
    
    # Créer un batch
    from torch.utils.data import DataLoader, Subset
    subset = Subset(param_dataset, range(min(4, len(param_dataset))))
    loader = DataLoader(subset, batch_size=2, shuffle=False)
    
    batch = next(iter(loader))
    
    print(f"Batch keys: {list(batch.keys())}")
    print(f"Scenario features shape: {batch['scenario_features'].shape}")
    print(f"Grid features shape: {batch['grid_features'].shape}")
    print(f"Param targets shape: {batch['param_targets'].shape}")
    
    # Forward pass
    trainer.model.eval()
    with torch.no_grad():
        scenario_features = batch['scenario_features']
        grid_features = batch['grid_features']
        predictions = trainer.model(scenario_features, grid_features)
        
        print(f"Predictions shape: {predictions.shape}")
        print(f"Predictions sample: {predictions[0][:5]}")  # Premiers 5 éléments
    
    # Vérifications
    assert predictions.shape[0] == scenario_features.shape[0], "Batch size incorrect"
    assert predictions.shape[1] == 10, "Output dimension incorrecte"
    assert not torch.isnan(predictions).any(), "Predictions ne doivent pas contenir de NaN"
    
    print("✓ Forward pass HRM OK")


def test_hrm_training_step():
    """Test d'une étape d'entraînement HRM."""
    print("\n=== Test étape d'entraînement HRM ===")
    
    config = HRMTrainingConfig(
        hidden_dim=32,
        batch_size=2,
        log_every=1,
        device="cpu"
    )
    
    trainer = HRMParameterTrainer(config)
    base_dataset = create_mock_hrm_dataset()
    param_dataset = ParameterResolutionDataset(base_dataset, config, mode="train")
    
    if len(param_dataset) == 0:
        print("⚠ Pas d'exemples générés, test ignoré")
        return
    
    # DataLoader avec seulement quelques échantillons
    from torch.utils.data import DataLoader, Subset
    subset = Subset(param_dataset, range(min(4, len(param_dataset))))
    loader = DataLoader(subset, batch_size=2, shuffle=False)
    
    print(f"Nombre de batches: {len(loader)}")
    
    # Une étape d'entraînement
    metrics = trainer.train_epoch(loader)
    
    print(f"Métriques d'entraînement HRM:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # Vérifications
    assert 'loss' in metrics, "Doit retourner la perte"
    assert metrics['loss'] >= 0, "Perte doit être positive ou nulle"
    
    print("✓ Étape d'entraînement HRM OK")


def test_hrm_validation_step():
    """Test d'une étape de validation HRM."""
    print("\n=== Test étape de validation HRM ===")
    
    config = HRMTrainingConfig(hidden_dim=32, batch_size=2, device="cpu")
    
    trainer = HRMParameterTrainer(config)
    base_dataset = create_mock_hrm_dataset()
    param_dataset = ParameterResolutionDataset(base_dataset, config, mode="val")
    
    if len(param_dataset) == 0:
        print("⚠ Pas d'exemples générés, test ignoré")
        return
    
    # DataLoader
    from torch.utils.data import DataLoader, Subset
    subset = Subset(param_dataset, range(min(4, len(param_dataset))))
    loader = DataLoader(subset, batch_size=2, shuffle=False)
    
    # Une étape de validation
    metrics = trainer.validate_epoch(loader)
    
    print(f"Métriques de validation HRM:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # Vérifications
    assert 'loss' in metrics, "Doit retourner la perte"
    assert metrics['loss'] >= 0, "Perte doit être positive ou nulle"
    
    print("✓ Étape de validation HRM OK")


def test_hrm_checkpoint_system():
    """Test du système de checkpoints HRM."""
    print("\n=== Test système de checkpoints HRM ===")
    
    config = HRMTrainingConfig(
        hidden_dim=32,
        checkpoint_dir="test_hrm_checkpoints",
        device="cpu"
    )
    
    trainer = HRMParameterTrainer(config)
    
    # Sauvegarder un checkpoint
    test_metrics = {'loss': 2.5}
    trainer.save_checkpoint(3, 'test_hrm_checkpoint.pth', test_metrics)
    
    checkpoint_path = os.path.join(config.checkpoint_dir, 'test_hrm_checkpoint.pth')
    assert os.path.exists(checkpoint_path), "Checkpoint HRM doit être sauvegardé"
    
    # Charger le checkpoint
    epoch, metrics = trainer.load_checkpoint(checkpoint_path)
    
    print(f"Checkpoint HRM chargé - Époque: {epoch}, Métriques: {metrics}")
    
    assert epoch == 3, "Époque incorrecte"
    assert metrics['loss'] == 2.5, "Métriques incorrectes"
    
    # Nettoyer
    import shutil
    if os.path.exists(config.checkpoint_dir):
        shutil.rmtree(config.checkpoint_dir)
    
    print("✓ Système de checkpoints HRM OK")


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements HRM ===")
    
    # Requirement 7.2: Entraînement HRM sur résolution de paramètres
    config = HRMTrainingConfig(device="cpu")
    base_dataset = create_mock_hrm_dataset()
    param_dataset = ParameterResolutionDataset(base_dataset, config, mode="train")
    
    # Vérifier que le dataset génère des exemples scénario → paramètres
    if len(param_dataset) > 0:
        sample = param_dataset[0]
        assert 'scenario_features' in sample, "Doit avoir des features de scénario"
        assert 'param_targets' in sample, "Doit avoir des targets de paramètres"
        print("✓ Requirement 7.2: Dataset scénario → paramètres OK")
    else:
        print("⚠ Requirement 7.2: Pas d'exemples générés (normal avec mock data)")
    
    # Vérifier que le système d'entraînement est fonctionnel
    trainer = HRMParameterTrainer(HRMTrainingConfig(hidden_dim=32, device="cpu"))
    assert hasattr(trainer, 'model'), "Doit avoir un modèle"
    assert hasattr(trainer, 'optimizer'), "Doit avoir un optimiseur"
    
    print("✓ Requirements d'entraînement HRM OK")


def main():
    """Fonction principale de test."""
    print("Test de l'entraînement HRM Parameter Resolution")
    print("=" * 50)
    
    success = True
    
    try:
        test_hrm_training_config()
        test_parameter_resolution_dataset()
        test_hrm_parameter_net()
        test_hrm_trainer_creation()
        test_hrm_forward_pass()
        test_hrm_training_step()
        test_hrm_validation_step()
        test_hrm_checkpoint_system()
        test_requirements_compliance()
        
        print("\n🎉 Tous les tests sont passés!")
        print("Le système d'entraînement HRM Parameter Resolution est prêt")
        
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)