# Configuration FP16 avec gradient checkpointing
model = GrammarConstrainedHRM(
    model_dim=192,
    n_heads=8,
    grammar_vocab_size=len(tokenizer.vocab),
    grammar_validator=grammar_val
).half()

# Activation du gradient checkpointing
from torch.utils.checkpoint import checkpoint

class CheckpointedHRM(GrammarConstrainedHRM):
    def hierarchical_reasoning(self, grid_emb):
        return checkpoint(super().hierarchical_reasoning, grid_emb)