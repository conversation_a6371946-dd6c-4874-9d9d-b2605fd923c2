#!/usr/bin/env python3
"""
Script de test pour le MultiExampleDataset.

Valide que le dataset charge correctement tous les exemples train
et normalise les grilles comme spécifié.
"""

import sys
import os
sys.path.append('.')

from src.multi_example_dataset import MultiExampleDataset, MultiExampleDatasetWithPrograms
from src.tokenizer import GrammarTokenizer


def test_basic_functionality():
    """Test des fonctionnalités de base du MultiExampleDataset."""
    print("=== Test MultiExampleDataset ===")
    
    # Créer le dataset
    dataset = MultiExampleDataset('arcdata/training')
    
    print(f"Nombre de puzzles chargés: {len(dataset)}")
    
    if len(dataset) == 0:
        print("ERREUR: Aucun puzzle chargé!")
        return False
    
    # Tester le premier élément
    sample = dataset[0]
    
    print(f"Puzzle ID: {sample['puzzle_id']}")
    print(f"Nombre d'exemples train: {len(sample['train_pairs'])}")
    print(f"Forme test_input: {sample['test_input'].shape}")
    
    # Vérifier les formes des grilles
    for i, (input_grid, output_grid) in enumerate(sample['train_pairs']):
        print(f"Train {i+1} - Input: {input_grid.shape}, Output: {output_grid.shape}")
        
        # Vérifier que les grilles sont bien normalisées à 30x30
        assert input_grid.shape == (30, 30), f"Input {i} mal normalisé: {input_grid.shape}"
        assert output_grid.shape == (30, 30), f"Output {i} mal normalisé: {output_grid.shape}"
    
    assert sample['test_input'].shape == (30, 30), f"Test input mal normalisé: {sample['test_input'].shape}"
    
    print("✓ Normalisation des grilles OK")
    
    # Afficher les statistiques
    stats = dataset.get_puzzle_stats()
    print("\n=== Statistiques du dataset ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    return True


def test_with_programs():
    """Test du MultiExampleDatasetWithPrograms."""
    print("\n=== Test MultiExampleDatasetWithPrograms ===")
    
    # Créer le tokenizer
    try:
        tokenizer = GrammarTokenizer()
    except Exception as e:
        print(f"ATTENTION: Erreur création tokenizer: {e}, test avec tokenizer None")
        tokenizer = None
    
    # Créer le dataset avec programmes
    dataset = MultiExampleDatasetWithPrograms('arcdata/training', tokenizer)
    
    if len(dataset) == 0:
        print("ERREUR: Aucun puzzle chargé!")
        return False
    
    # Tester un échantillon
    sample = dataset[0]
    
    print(f"Puzzle ID: {sample['puzzle_id']}")
    print(f"Programme disponible: {sample['program_text'] is not None}")
    
    if sample['program_tokens'] is not None:
        print(f"Tokens programme: {sample['program_tokens'].shape}")
        print(f"Programme: {sample['program_text'][:100]}...")
    
    return True


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    dataset = MultiExampleDataset('arcdata/training')
    
    if len(dataset) == 0:
        print("ERREUR: Pas de données pour tester")
        return False
    
    sample = dataset[0]
    
    # Requirement 1.1: Retourner tous les exemples train
    assert len(sample['train_pairs']) >= 1, "Doit avoir au moins 1 exemple train"
    print(f"✓ Requirement 1.1: {len(sample['train_pairs'])} exemples train chargés")
    
    # Requirement 1.2: Normalisation à 30x30
    for input_grid, output_grid in sample['train_pairs']:
        assert input_grid.shape == (30, 30), "Input doit être 30x30"
        assert output_grid.shape == (30, 30), "Output doit être 30x30"
    assert sample['test_input'].shape == (30, 30), "Test input doit être 30x30"
    print("✓ Requirement 1.2: Normalisation 30x30 OK")
    
    # Requirement 1.3: N paires pour N exemples
    n_examples = len(sample['train_pairs'])
    assert len(sample['train_pairs']) == n_examples, "Nombre de paires incorrect"
    print(f"✓ Requirement 1.3: {n_examples} paires pour {n_examples} exemples")
    
    return True


def main():
    """Fonction principale de test."""
    print("Test du MultiExampleDataset pour DINO-HRM-ARC")
    print("=" * 50)
    
    success = True
    
    try:
        success &= test_basic_functionality()
        success &= test_with_programs()
        success &= test_requirements_compliance()
        
        if success:
            print("\n🎉 Tous les tests sont passés!")
            print("Le MultiExampleDataset est prêt pour DINO-HRM-ARC")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)