"""
Trainer amélioré pour HRM avec deep supervision et Q-learning ACT
Implémente les techniques du paper HRM
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm
import numpy as np
from typing import Dict, List, Optional
import json
import os
from datetime import datetime

from tests.test_improved_hrm import ImprovedHRM
from src.grammar_validator import validate_generated_program

class ImprovedHRMTrainer:
    """
    Trainer pour HRM avec :
    - Deep supervision (supervision à chaque segment)
    - Q-learning ACT (adaptive computation time)
    - Gradient 1-step approximation
    - Validation grammaticale intégrée
    """
    
    def __init__(self, model: ImprovedHRM, tokenizer, device="cuda"):
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        self.model.to(device)
        
        # Optimiseur Adam-atan2 comme dans le paper (fallback vers AdamW)
        self.optimizer = optim.AdamW(
            model.parameters(),
            lr=3e-4,
            weight_decay=0.01,
            betas=(0.9, 0.95)
        )
        
        # Scheduler avec warmup linéaire
        self.scheduler = None
        
        # Losses
        self.program_loss_fn = nn.CrossEntropyLoss(ignore_index=tokenizer.vocab['<pad>'])
        self.q_loss_fn = nn.MSELoss()
        
        # Métriques
        self.training_history = []
        self.validation_history = []
        
        print(f"Trainer initialisé sur {device}")
    
    def setup_scheduler(self, total_steps, warmup_steps=1000):
        """Configure le scheduler avec warmup linéaire"""
        def lr_lambda(step):
            if step < warmup_steps:
                return step / warmup_steps
            return 1.0
        
        self.scheduler = optim.lr_scheduler.LambdaLR(self.optimizer, lr_lambda)
    
    def compute_deep_supervision_loss(self, segments, targets, q_values_list, rewards):
        """
        Calcule la loss avec deep supervision
        Chaque segment contribue à la loss totale
        """
        total_program_loss = 0
        total_q_loss = 0
        
        for i, (segment_logits, q_values) in enumerate(zip(segments, q_values_list)):
            # Loss du programme pour ce segment
            logits = segment_logits[:, :-1, :]  # Enlever le dernier token
            segment_targets = targets[:, 1:]    # Décaler pour teacher forcing
            
            program_loss = self.program_loss_fn(
                logits.reshape(-1, logits.size(-1)),
                segment_targets.reshape(-1)
            )
            total_program_loss += program_loss
            
            # Q-learning loss
            # Récompense = 1 si le programme est correct, 0 sinon
            batch_rewards = rewards if rewards is not None else torch.zeros(q_values.size(0), device=self.device)
            
            # Targets Q-learning simplifiés
            q_targets = torch.zeros_like(q_values)
            q_targets[:, 0] = batch_rewards  # halt
            q_targets[:, 1] = 1 - batch_rewards  # continue
            
            q_loss = self.q_loss_fn(q_values, q_targets)
            total_q_loss += q_loss
        
        # Pondération des losses
        total_loss = total_program_loss + 0.1 * total_q_loss
        
        return {
            'total_loss': total_loss,
            'program_loss': total_program_loss,
            'q_loss': total_q_loss,
            'num_segments': len(segments)
        }
    
    def evaluate_program_quality(self, generated_tokens, target_tokens=None):
        """
        Évalue la qualité d'un programme généré
        Utilise la validation grammaticale
        """
        program_str = self.tokenizer.detokenize(generated_tokens)
        validation = validate_generated_program(program_str, self.tokenizer)
        
        quality_score = 0.0
        
        # Score basé sur la validité grammaticale
        if validation['is_valid']:
            quality_score += 0.5
        
        # Score basé sur les statistiques
        stats = validation['statistics']
        if stats['has_init']:
            quality_score += 0.2
        if stats['has_end']:
            quality_score += 0.1
        if stats['unk_tokens'] == 0:
            quality_score += 0.1
        if stats['malformed_coords'] == 0:
            quality_score += 0.1
        
        return quality_score, validation
    
    def train_epoch(self, dataloader: DataLoader, epoch: int):
        """
        Entraîne le modèle pour une époque avec deep supervision
        """
        self.model.train()
        epoch_losses = []
        epoch_metrics = {
            'program_loss': [],
            'q_loss': [],
            'quality_scores': [],
            'valid_programs': 0,
            'total_programs': 0
        }
        
        progress = tqdm(dataloader, desc=f"Epoch {epoch+1}")
        
        for batch_idx, (grids, programs) in enumerate(progress):
            grids = grids.to(self.device)
            programs = programs.to(self.device)
            
            self.optimizer.zero_grad()
            
            # Forward pass avec deep supervision
            segments, q_values_list, _ = self.model.forward_with_deep_supervision(
                grids, programs, max_segments=3
            )
            
            # Évaluer la qualité des programmes (pour récompenses Q-learning)
            batch_rewards = []
            for i in range(grids.size(0)):
                # Générer un programme pour cette grille
                generated = self.model.generate_program(grids[i], max_length=100)
                quality_score, validation = self.evaluate_program_quality(generated)
                batch_rewards.append(quality_score)
                
                # Métriques
                epoch_metrics['quality_scores'].append(quality_score)
                if validation['is_valid']:
                    epoch_metrics['valid_programs'] += 1
                epoch_metrics['total_programs'] += 1
            
            rewards = torch.tensor(batch_rewards, device=self.device)
            
            # Calcul de la loss avec deep supervision
            loss_dict = self.compute_deep_supervision_loss(
                segments, programs, q_values_list, rewards
            )
            
            # Backpropagation
            loss_dict['total_loss'].backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            if self.scheduler:
                self.scheduler.step()
            
            # Métriques
            epoch_losses.append(loss_dict['total_loss'].item())
            epoch_metrics['program_loss'].append(loss_dict['program_loss'].item())
            epoch_metrics['q_loss'].append(loss_dict['q_loss'].item())
            
            # Mise à jour de la barre de progression
            progress.set_postfix({
                'loss': f"{loss_dict['total_loss'].item():.4f}",
                'prog_loss': f"{loss_dict['program_loss'].item():.4f}",
                'q_loss': f"{loss_dict['q_loss'].item():.4f}",
                'quality': f"{np.mean(epoch_metrics['quality_scores'][-10:]):.3f}" if epoch_metrics['quality_scores'] else "0.000"
            })
        
        # Statistiques de l'époque
        avg_loss = np.mean(epoch_losses)
        avg_quality = np.mean(epoch_metrics['quality_scores']) if epoch_metrics['quality_scores'] else 0
        valid_ratio = epoch_metrics['valid_programs'] / epoch_metrics['total_programs'] if epoch_metrics['total_programs'] > 0 else 0
        
        epoch_summary = {
            'epoch': epoch + 1,
            'avg_loss': avg_loss,
            'avg_program_loss': np.mean(epoch_metrics['program_loss']),
            'avg_q_loss': np.mean(epoch_metrics['q_loss']),
            'avg_quality_score': avg_quality,
            'valid_program_ratio': valid_ratio,
            'learning_rate': self.optimizer.param_groups[0]['lr']
        }
        
        self.training_history.append(epoch_summary)
        
        print(f"Epoch {epoch+1} Summary:")
        print(f"  Loss: {avg_loss:.4f}")
        print(f"  Quality Score: {avg_quality:.3f}")
        print(f"  Valid Programs: {valid_ratio:.1%}")
        
        return epoch_summary
    
    def validate(self, dataloader: DataLoader, num_samples=50):
        """
        Validation du modèle avec génération de programmes
        """
        self.model.eval()
        validation_results = []
        
        with torch.no_grad():
            for i, (grids, programs) in enumerate(dataloader):
                if i >= num_samples:
                    break
                
                grids = grids.to(self.device)
                
                for j in range(min(2, grids.size(0))):  # Max 2 par batch
                    grid = grids[j]
                    target_program = programs[j]
                    
                    # Génération
                    generated_tokens = self.model.generate_program(grid, max_length=150)
                    
                    # Évaluation
                    quality_score, validation = self.evaluate_program_quality(generated_tokens)
                    
                    result = {
                        'grid_shape': grid.shape,
                        'generated_program': self.tokenizer.detokenize(generated_tokens),
                        'target_program': self.tokenizer.detokenize(target_program.tolist()),
                        'quality_score': quality_score,
                        'is_valid': validation['is_valid'],
                        'validation_error': validation['error'],
                        'suggestions': validation['suggestions']
                    }
                    
                    validation_results.append(result)
        
        # Statistiques de validation
        avg_quality = np.mean([r['quality_score'] for r in validation_results])
        valid_ratio = np.mean([r['is_valid'] for r in validation_results])
        
        validation_summary = {
            'avg_quality_score': avg_quality,
            'valid_program_ratio': valid_ratio,
            'num_samples': len(validation_results),
            'sample_results': validation_results[:5]  # Premiers 5 exemples
        }
        
        self.validation_history.append(validation_summary)
        
        print(f"Validation Results:")
        print(f"  Quality Score: {avg_quality:.3f}")
        print(f"  Valid Programs: {valid_ratio:.1%}")
        
        return validation_summary
    
    def train(self, train_dataloader, val_dataloader=None, epochs=50, save_every=10):
        """
        Boucle d'entraînement complète
        """
        print(f"Début de l'entraînement pour {epochs} époques")
        
        # Setup scheduler
        total_steps = len(train_dataloader) * epochs
        self.setup_scheduler(total_steps)
        
        best_quality = 0.0
        
        for epoch in range(epochs):
            # Entraînement
            epoch_summary = self.train_epoch(train_dataloader, epoch)
            
            # Validation
            if val_dataloader and (epoch + 1) % 5 == 0:
                val_summary = self.validate(val_dataloader)
                
                # Sauvegarde du meilleur modèle
                if val_summary['avg_quality_score'] > best_quality:
                    best_quality = val_summary['avg_quality_score']
                    self.save_model(f"best_hrm_improved.pth")
                    print(f"Nouveau meilleur modèle sauvegardé (quality: {best_quality:.3f})")
            
            # Sauvegarde périodique
            if (epoch + 1) % save_every == 0:
                self.save_model(f"hrm_improved_epoch_{epoch+1}.pth")
        
        # Sauvegarde finale
        self.save_model("hrm_improved_final.pth")
        self.save_training_history()
        
        print("Entraînement terminé !")
    
    def save_model(self, filename):
        """Sauvegarde le modèle et les métadonnées"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_history': self.training_history,
            'validation_history': self.validation_history,
            'model_config': {
                'model_dim': self.model.model_dim,
                'N_cycles': self.model.N,
                'T_steps': self.model.T,
                'vocab_size': self.model.vocab_size
            }
        }, filename)
    
    def save_training_history(self):
        """Sauvegarde l'historique d'entraînement"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        history = {
            'training_history': self.training_history,
            'validation_history': self.validation_history,
            'timestamp': timestamp
        }
        
        with open(f"training_history_{timestamp}.json", 'w') as f:
            json.dump(history, f, indent=2)

def train_improved_hrm():
    """Lance l'entraînement du HRM amélioré"""
    from config import Config
    from src.arc_dataset import ArcDataset
    from torch.utils.data import DataLoader
    
    # Configuration
    cfg = Config()
    device = cfg.DEVICE
    
    # Dataset
    tokenizer = GrammarTokenizer()
    dataset = ArcDataset(cfg.DATA_DIR, tokenizer, cfg.MAX_PROG_LEN)
    
    # Split train/val
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    train_dataloader = DataLoader(train_dataset, batch_size=cfg.BATCH_SIZE, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=cfg.BATCH_SIZE, shuffle=False)
    
    # Modèle
    model = ImprovedHRM(
        model_dim=cfg.MODEL_DIM,
        n_heads=cfg.N_HEADS,
        grammar_vocab_size=len(tokenizer.vocab),
        N_cycles=cfg.N_CYCLES,
        T_steps=cfg.T_STEPS
    )
    
    # Trainer
    trainer = ImprovedHRMTrainer(model, tokenizer, device)
    
    # Entraînement
    trainer.train(train_dataloader, val_dataloader, epochs=cfg.EPOCHS)

if __name__ == "__main__":
    train_improved_hrm()