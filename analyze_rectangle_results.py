#!/usr/bin/env python3
"""
Analyse des résultats de détection de rectangles sur les puzzles mosaïques
"""

import json
import numpy as np
from collections import defaultdict

def analyze_rectangle_results():
    """Analyse les résultats de détection de rectangles"""
    
    # Charger les résultats
    with open("rectangle_detector_mosaic_results.json", 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("=== ANALYSE DES RÉSULTATS DE DÉTECTION DE RECTANGLES ===\n")
    
    # Statistiques globales
    total_tasks = len(results)
    total_train_examples = sum(len(task['train_examples']) for task in results.values())
    total_test_examples = sum(len(task['test_examples']) for task in results.values())
    
    print(f"Tâches analysées: {total_tasks}")
    print(f"Exemples d'entraînement: {total_train_examples}")
    print(f"Exemples de test: {total_test_examples}")
    print()
    
    # Analyse par tâche
    task_stats = []
    
    for task_id, task_data in results.items():
        task_stat = {
            'task_id': task_id,
            'train_count': len(task_data['train_examples']),
            'test_count': len(task_data['test_examples']),
            'input_rect_counts': [],
            'output_rect_counts': [],
            'avg_input_rects': 0,
            'avg_output_rects': 0
        }
        
        # Compter les rectangles dans les exemples d'entraînement
        for example in task_data['train_examples']:
            if 'input_rect_count' in example:
                task_stat['input_rect_counts'].append(example['input_rect_count'])
            if 'output_rect_count' in example:
                task_stat['output_rect_counts'].append(example['output_rect_count'])
        
        # Compter les rectangles dans les exemples de test
        for example in task_data['test_examples']:
            if 'input_rect_count' in example:
                task_stat['input_rect_counts'].append(example['input_rect_count'])
        
        # Calculer les moyennes
        if task_stat['input_rect_counts']:
            task_stat['avg_input_rects'] = np.mean(task_stat['input_rect_counts'])
        if task_stat['output_rect_counts']:
            task_stat['avg_output_rects'] = np.mean(task_stat['output_rect_counts'])
        
        task_stats.append(task_stat)
    
    # Trier par nombre moyen de rectangles d'entrée
    task_stats.sort(key=lambda x: x['avg_input_rects'], reverse=True)
    
    print("=== TOP 10 TÂCHES AVEC LE PLUS DE RECTANGLES DÉTECTÉS ===")
    for i, stat in enumerate(task_stats[:10]):
        print(f"{i+1:2d}. {stat['task_id']} - "
              f"Moy. input: {stat['avg_input_rects']:6.1f} rectangles, "
              f"Moy. output: {stat['avg_output_rects']:6.1f} rectangles")
    
    print("\n=== TOP 10 TÂCHES AVEC LE MOINS DE RECTANGLES DÉTECTÉS ===")
    for i, stat in enumerate(task_stats[-10:]):
        print(f"{i+1:2d}. {stat['task_id']} - "
              f"Moy. input: {stat['avg_input_rects']:6.1f} rectangles, "
              f"Moy. output: {stat['avg_output_rects']:6.1f} rectangles")
    
    # Analyse des patterns
    print("\n=== ANALYSE DES PATTERNS ===")
    
    # Tâches où output a plus de rectangles que input
    more_output = [s for s in task_stats if s['avg_output_rects'] > s['avg_input_rects']]
    print(f"Tâches où output > input: {len(more_output)}")
    
    # Tâches où output a moins de rectangles que input  
    less_output = [s for s in task_stats if s['avg_output_rects'] < s['avg_input_rects']]
    print(f"Tâches où output < input: {len(less_output)}")
    
    # Tâches où output ≈ input
    same_output = [s for s in task_stats if abs(s['avg_output_rects'] - s['avg_input_rects']) < 1]
    print(f"Tâches où output ≈ input: {len(same_output)}")
    
    # Distribution des nombres de rectangles
    all_input_counts = []
    all_output_counts = []
    
    for stat in task_stats:
        all_input_counts.extend(stat['input_rect_counts'])
        all_output_counts.extend(stat['output_rect_counts'])
    
    print(f"\n=== STATISTIQUES GLOBALES ===")
    print(f"Rectangles input - Min: {min(all_input_counts)}, Max: {max(all_input_counts)}, "
          f"Moyenne: {np.mean(all_input_counts):.1f}, Médiane: {np.median(all_input_counts):.1f}")
    print(f"Rectangles output - Min: {min(all_output_counts)}, Max: {max(all_output_counts)}, "
          f"Moyenne: {np.mean(all_output_counts):.1f}, Médiane: {np.median(all_output_counts):.1f}")
    
    # Analyse détaillée de quelques cas intéressants
    print(f"\n=== CAS INTÉRESSANTS ===")
    
    # Cas avec beaucoup de rectangles
    high_rect_tasks = [s for s in task_stats if s['avg_input_rects'] > 200]
    print(f"\nTâches avec >200 rectangles en moyenne:")
    for stat in high_rect_tasks:
        print(f"  {stat['task_id']}: {stat['avg_input_rects']:.1f} rectangles")
    
    # Cas avec très peu de rectangles
    low_rect_tasks = [s for s in task_stats if s['avg_input_rects'] < 10]
    print(f"\nTâches avec <10 rectangles en moyenne:")
    for stat in low_rect_tasks:
        print(f"  {stat['task_id']}: {stat['avg_input_rects']:.1f} rectangles")
    
    # Cas où la transformation réduit drastiquement le nombre de rectangles
    big_reduction = [s for s in task_stats 
                    if s['avg_output_rects'] > 0 and 
                    s['avg_input_rects'] / s['avg_output_rects'] > 10]
    print(f"\nTâches avec forte réduction (ratio >10:1):")
    for stat in big_reduction:
        ratio = stat['avg_input_rects'] / stat['avg_output_rects']
        print(f"  {stat['task_id']}: {stat['avg_input_rects']:.1f} → {stat['avg_output_rects']:.1f} "
              f"(ratio: {ratio:.1f}:1)")

if __name__ == "__main__":
    analyze_rectangle_results()