import numpy as np
import json
import os
from scipy.ndimage import label

def all_rectangles(mat):
    mat = np.asarray(mat)
    results = {}
    for val in np.unique(mat):
        mask = (mat == val)
        labeled, n = label(mask)
        rects = []
        for k in range(1, n+1):
            rows, cols = np.where(labeled == k)
            rmin, rmax = rows.min(), rows.max()
            cmin, cmax = cols.min(), cols.max()
            # Vérification : si le rectangle extrait est bien plein de val
            # ET qu'il fait plus de 4 cellules
            rect_size = (rmax - rmin + 1) * (cmax - cmin + 1)
            if np.all(mat[rmin:rmax+1, cmin:cmax+1] == val) and rect_size > 4:
                rects.append((rmin, cmin, rmax, cmax))
        if rects:
            results[int(val)] = [f"[{r1},{c1} {r2},{c2}]"
                               for r1, c1, r2, c2 in rects]
    return results

def load_arc_data():
    training_path = "arcdata/training"
    evaluation_path = "arcdata/evaluation"
    data = {}
    
    if os.path.exists(training_path):
        for filename in os.listdir(training_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]
                try:
                    with open(os.path.join(training_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except:
                    pass
    
    if os.path.exists(evaluation_path):
        for filename in os.listdir(evaluation_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]
                try:
                    with open(os.path.join(evaluation_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except:
                    pass
    
    return data

def get_mosaic_puzzle_ids():
    mosaic_ids = []
    encodings = ['utf-8', 'latin-1', 'cp1252']
    
    for encoding in encodings:
        try:
            with open("AnalysesGrilles/final_mosaic_puzzle_ids.txt", 'r', encoding=encoding) as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        task_id = line.split()[0]
                        if len(task_id) == 8:
                            mosaic_ids.append(task_id)
            break
        except:
            continue
    
    return mosaic_ids

# Test sur tous les inputs des puzzles mosaïques
arc_data = load_arc_data()
mosaic_ids = get_mosaic_puzzle_ids()

for task_id in mosaic_ids:
    if task_id not in arc_data:
        continue
    
    task_data = arc_data[task_id]
    print(f"\n=== {task_id} ===")
    
    # Exemples d'entraînement
    for i, example in enumerate(task_data.get('train', [])):
        input_grid = np.array(example['input'])
        zones = all_rectangles(input_grid)
        if zones:
            print(f"Train {i+1} input:")
            for val, rects in zones.items():
                print(f"Valeur {val} : {rects}")
    
    # Exemples de test
    for i, example in enumerate(task_data.get('test', [])):
        input_grid = np.array(example['input'])
        zones = all_rectangles(input_grid)
        if zones:
            print(f"Test {i+1} input:")
            for val, rects in zones.items():
                print(f"Valeur {val} : {rects}")