class GrammarAwareTokenizer:
    def __init__(self, grammar_validator):
        self.validator = grammar_validator
        self.vocab = self._build_vocab()
        
    def _build_vocab(self):
        """Construit le vocabulaire à partir de la grammaire Lark"""
        grammar = self.validator.grammar
        tokens = re.findall(r'[A-Z_]+:|\b\w+\b', grammar)
        unique_tokens = set(token.strip(':') for token in tokens if token.isupper() or '_' in token)
        return {token: idx for idx, token in enumerate(sorted(unique_tokens))}
    
    def tokenize(self, program):
        """Tokenization alignée sur la grammaire"""
        cleaned = self.validator._clean_program(program)
        try:
            tree = self.validator.parser.parse(cleaned)
            return [self.vocab[t.type] for t in tree.scan_values(lambda x: isinstance(x, v_args.Token))]
        except LarkError:
            # Fallback pour programmes invalides
            tokens = re.findall(r'[A-Z]+|\d+|\[|\]|\(|\)|,', cleaned)
            return [self.vocab.get(t, self.vocab['<unk>']) for t in tokens]