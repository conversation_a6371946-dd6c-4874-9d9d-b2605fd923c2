import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

class ValueEmbedding(nn.Module):
    """Embedding intelligent des valeurs de grille avec contexte"""
    def __init__(self, vocab_size=10, embed_dim=128, context_size=3):
        super().__init__()
        self.value_embed = nn.Embedding(vocab_size, embed_dim)
        self.context_conv = nn.Conv2d(embed_dim, embed_dim, 
                                     kernel_size=context_size, 
                                     padding=context_size//2)
        
    def forward(self, grid):
        # grid: [batch, height, width]
        val_emb = self.value_embed(grid)  # [batch, H, W, D]
        
        # Contexte spatial
        val_emb_perm = val_emb.permute(0, 3, 1, 2)  # [batch, D, H, W]
        context_emb = self.context_conv(val_emb_perm)
        context_emb = context_emb.permute(0, 2, 3, 1)  # [batch, H, W, D]
        
        return val_emb + context_emb

class PositionalEncoding2D(nn.Module):
    """Encodage positionnel 2D pour grilles"""
    def __init__(self, dim, max_size=30):
        super().__init__()
        self.row_enc = nn.Embedding(max_size, dim//2)
        self.col_enc = nn.Embedding(max_size, dim//2)
        
    def forward(self, x):
        # x: [batch, H, W, D]
        batch, height, width, _ = x.shape
        
        # Générer les positions
        rows = torch.arange(height, device=x.device).view(1, height, 1, 1)
        cols = torch.arange(width, device=x.device).view(1, 1, width, 1)
        
        # Embeddings de position
        row_emb = self.row_enc(rows).expand(batch, -1, width, -1)
        col_emb = self.col_enc(cols).expand(batch, height, -1, -1)
        
        # Combinaison
        pos_emb = torch.cat([row_emb, col_emb], dim=-1)
        return x + pos_emb

class GeometricAttention(nn.Module):
    """Attention spatiale avec contraintes géométriques"""
    def __init__(self, dim, num_heads=8, grid_size=30):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        # Projections pour Q, K, V
        self.to_qkv = nn.Linear(dim, dim * 3, bias=False)
        
        # Embedding des distances
        max_dist = 2 * (grid_size - 1)
        self.dist_embed = nn.Embedding(max_dist + 1, num_heads)
        
        # Output projection
        self.to_out = nn.Linear(dim, dim)
        
    def forward(self, x):
        batch, height, width, _ = x.shape
        total_pos = height * width
        
        # Générer les indices de distance
        row_diff = torch.abs(torch.arange(height).unsqueeze(1) - torch.arange(height).unsqueeze(0))
        col_diff = torch.abs(torch.arange(width).unsqueeze(1) - torch.arange(width).unsqueeze(0))
        dist_matrix = (row_diff.unsqueeze(2) + col_diff.unsqueeze(0)).flatten()
        dist_emb = self.dist_embed(dist_matrix).view(height, width, height, width, self.num_heads)
        dist_emb = dist_emb.permute(4, 0, 1, 2, 3)  # [heads, H, W, H, W]
        
        # Projection Q, K, V
        qkv = self.to_qkv(x).chunk(3, dim=-1)
        q, k, v = map(lambda t: t.view(batch, height, width, self.num_heads, self.head_dim).permute(0, 3, 1, 2, 4), qkv)
        
        # Calcul des scores d'attention
        attn = torch.einsum('bnhwd,bnhwk->bnhwk', q, k) * self.scale
        attn = attn + dist_emb.unsqueeze(0)
        
        # Softmax et agrégation
        attn = attn.softmax(dim=-1)
        out = torch.einsum('bnhwk,bnhwk->bnhwd', attn, v)
        out = out.permute(0, 2, 3, 1, 4).contiguous().view(batch, height, width, -1)
        
        return self.to_out(out)

class TransformerBlock(nn.Module):
    """Bloc Transformer spatial complet"""
    def __init__(self, dim, num_heads, grid_size=30):
        super().__init__()
        self.norm1 = nn.LayerNorm(dim)
        self.attn = GeometricAttention(dim, num_heads, grid_size)
        self.norm2 = nn.LayerNorm(dim)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim)
        )
        
    def forward(self, x):
        # Attention résiduelle
        x = x + self.attn(self.norm1(x))
        
        # MLP résiduel
        x = x + self.mlp(self.norm2(x))
        return x

class PatternDetectionHead(nn.Module):
    """Tête de détection de motifs spécifiques"""
    def __init__(self, dim, pattern_type):
        super().__init__()
        self.pattern_type = pattern_type
        
        if pattern_type == 'line':
            self.conv = nn.Conv2d(dim, 1, kernel_size=(3, 1), padding=(1, 0))
        elif pattern_type == 'column':
            self.conv = nn.Conv2d(dim, 1, kernel_size=(1, 3), padding=(0, 1))
        elif pattern_type == 'symmetry':
            self.conv = nn.Conv2d(dim, 1, kernel_size=3, padding=1)
        elif pattern_type == 'repetition':
            self.conv = nn.Conv2d(dim, 1, kernel_size=5, padding=2)
        
    def forward(self, x):
        # x: [batch, H, W, D]
        x = x.permute(0, 3, 1, 2)  # [batch, D, H, W]
        return self.conv(x).squeeze(1)  # [batch, H, W]

class GridDINO(nn.Module):
    """Modèle principal GridDINO"""
    def __init__(self, 
                 grid_size=30, 
                 vocab_size=10, 
                 embed_dim=128, 
                 num_layers=6, 
                 num_heads=8,
                 pattern_heads=['line', 'column', 'symmetry', 'repetition']):
        super().__init__()
        self.grid_size = grid_size
        
        # Embeddings
        self.value_embed = ValueEmbedding(vocab_size, embed_dim)
        self.pos_enc = PositionalEncoding2D(embed_dim, grid_size)
        
        # Transformer spatial
        self.blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, grid_size)
            for _ in range(num_layers)
        ])
        
        # Têtes de motifs
        self.pattern_heads = nn.ModuleDict({
            ptype: PatternDetectionHead(embed_dim, ptype)
            for ptype in pattern_heads
        })
        
        # Tête de reconstruction
        self.recon_head = nn.Sequential(
            nn.LayerNorm(embed_dim),
            nn.Linear(embed_dim, vocab_size))
        
    def forward(self, grid):
        # Embedding de base
        x = self.value_embed(grid)
        x = self.pos_enc(x)
        
        # Blocs de transformation
        for block in self.blocks:
            x = block(x)
        
        # Détection de motifs
        pattern_outputs = {}
        for ptype, head in self.pattern_heads.items():
            pattern_outputs[ptype] = head(x)
        
        # Reconstruction (pour pré-entraînement)
        recon_output = self.recon_head(x)
        
        return x, pattern_outputs, recon_output

class GridDINOPretrainer(nn.Module):
    """Module complet pour le pré-entraînement de GridDINO"""
    def __init__(self, model, mask_ratio=0.15):
        super().__init__()
        self.model = model
        self.mask_ratio = mask_ratio
        self.mask_token = nn.Parameter(torch.randn(1, 1, 1, model.value_embed.value_embed.embedding_dim))
        
    def apply_mask(self, grid):
        """Applique un masque aléatoire sur la grille"""
        batch, h, w = grid.shape
        num_mask = int(h * w * self.mask_ratio)
        
        # Générer des positions masquées
        mask_pos = torch.rand(batch, h, w).topk(num_mask, dim=-1).indices
        
        # Créer la grille masquée
        masked_grid = grid.clone()
        masked_grid.scatter_(-1, mask_pos, 10)  # Valeur spéciale pour masqué
        
        # Créer le masque booléen
        mask = torch.zeros_like(grid, dtype=torch.bool)
        mask.scatter_(-1, mask_pos, True)
        
        return masked_grid, mask
        
    def forward(self, grid):
        # Appliquer le masque
        masked_grid, mask = self.apply_mask(grid)
        
        # Passage dans le modèle
        features, pattern_outputs, recon_output = self.model(masked_grid)
        
        # Reconstruction des valeurs masquées
        recon_loss = F.cross_entropy(
            recon_output.view(-1, recon_output.shape[-1]),
            grid.view(-1),
            reduction='none'
        )
        recon_loss = (recon_loss.view_as(grid) * mask).sum() / mask.sum()
        
        # Calcul des motifs pour la vue contrastive
        pattern_features = torch.cat([p.flatten(1) for p in pattern_outputs.values()], dim=1)
        
        return {
            'features': features,
            'pattern_features': pattern_features,
            'recon_loss': recon_loss,
            'mask': mask
        }

class ContrastiveLoss(nn.Module):
    """Perte contrastive pour l'apprentissage de motifs"""
    def __init__(self, temperature=0.07):
        super().__init__()
        self.temperature = temperature
        
    def forward(self, features1, features2):
        # Normalisation des caractéristiques
        features1 = F.normalize(features1, dim=-1)
        features2 = F.normalize(features2, dim=-1)
        
        # Matrice de similarité
        sim_matrix = torch.einsum('bd,cd->bc', features1, features2) / self.temperature
        
        # Étiquettes positives sur la diagonale
        labels = torch.arange(features1.size(0), device=features1.device)
        
        # Perte contrastive
        loss = F.cross_entropy(sim_matrix, labels)
        return loss

# -----------------------------------------------------------
# Exemple d'utilisation
# -----------------------------------------------------------
if __name__ == "__main__":
    # Configuration
    grid_size = 30
    batch_size = 4
    vocab_size = 10
    
    # Créer une grille aléatoire
    grid = torch.randint(0, vocab_size, (batch_size, grid_size, grid_size))
    
    # Initialiser le modèle
    model = GridDINO(grid_size=grid_size, vocab_size=vocab_size)
    pretrainer = GridDINOPretrainer(model)
    
    # Passe avant
    outputs = pretrainer(grid)
    
    print("Features shape:", outputs['features'].shape)
    print("Recon loss:", outputs['recon_loss'].item())
    
    # Générer une vue augmentée pour l'apprentissage contrastif
    grid2 = torch.rot90(grid, 1, [1,2])
    outputs2 = pretrainer(grid2)
    
    # Calculer la perte contrastive
    contrastive_loss = ContrastiveLoss()
    loss = contrastive_loss(
        outputs['pattern_features'], 
        outputs2['pattern_features']
    )
    print("Contrastive loss:", loss.item())