def predict_transformation(input_grid, action_sequence):
    # Convertir en tenseur
    input_tensor = grid_to_tensor(input_grid)
    action_tensor = actions_to_tensor(action_sequence)
    
    # Prédiction
    with torch.no_grad():
        transformed, _, _ = model(input_tensor.unsqueeze(0), action_tensor.unsqueeze(0))
    
    # Convertir en grille
    return tensor_to_grid(transformed.squeeze(0))

# Exemple d'utilisation
input_grid = [...]  # Grille 30x30
actions = [("SELECT", "1,2:5,6"), ("ROTATE", "90"), ("PASTE", "10,10")]
result_grid = predict_transformation(input_grid, actions)