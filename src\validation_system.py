"""
Système de validation pour DINO-HRM-ARC.

Vérifie que la solution générée est cohérente avec tous les exemples train.
Implémente la logique d'ajustement des paramètres et de retry en cas d'échec.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import copy

from src.scenario_generalizer import GeneralizedScenario, ParameterConstraint, ParameterType
from models.hrm_parameter_resolver import HRMParameterResolver, ResolvedParameter, ParameterAnalyzer
from src.command_executor import CommandExecutor


class ValidationResult(Enum):
    """Résultats possibles de validation."""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILURE = "failure"
    ERROR = "error"


@dataclass
class ValidationReport:
    """Rapport détaillé de validation."""
    result: ValidationResult
    success_rate: float  # Pourcentage d'exemples validés avec succès
    successful_examples: int
    total_examples: int
    failed_examples: List[int]  # Indices des exemples qui ont échoué
    error_messages: List[str]
    execution_time: float = 0.0
    final_command: Optional[str] = None
    final_result: Optional[np.ndarray] = None


class ParameterAdjuster:
    """
    Ajusteur de paramètres pour améliorer les résultats de validation.
    
    Implémente différentes stratégies d'ajustement selon le type de paramètre
    et les patterns d'échec observés.
    """
    
    def __init__(self):
        self.adjustment_strategies = {
            ParameterType.COLOR: self._adjust_color_parameter,
            ParameterType.PATTERN: self._adjust_pattern_parameter,
            ParameterType.POSITION: self._adjust_position_parameter,
            ParameterType.SIZE: self._adjust_size_parameter,
            ParameterType.COUNT: self._adjust_count_parameter,
            ParameterType.DIRECTION: self._adjust_direction_parameter
        }
    
    def adjust_parameters(self, 
                         resolved_params: Dict[str, ResolvedParameter],
                         failed_examples: List[int],
                         train_pairs: List[Tuple[np.ndarray, np.ndarray]],
                         attempt: int) -> Dict[str, ResolvedParameter]:
        """
        Ajuste les paramètres basé sur les échecs de validation.
        
        Args:
            resolved_params: Paramètres actuels
            failed_examples: Indices des exemples qui ont échoué
            train_pairs: Exemples d'entraînement
            attempt: Numéro de la tentative (pour variation)
        
        Returns:
            Paramètres ajustés
        """
        adjusted_params = copy.deepcopy(resolved_params)
        
        # Analyser les patterns d'échec
        failure_analysis = self._analyze_failures(failed_examples, train_pairs)
        
        # Ajuster chaque paramètre selon sa stratégie
        for param_name, param in adjusted_params.items():
            if param.param_type in self.adjustment_strategies:
                strategy = self.adjustment_strategies[param.param_type]
                adjusted_params[param_name] = strategy(
                    param, failure_analysis, attempt
                )
        
        return adjusted_params
    
    def _analyze_failures(self, 
                         failed_examples: List[int],
                         train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> Dict[str, Any]:
        """Analyse les patterns d'échec pour guider l'ajustement."""
        analysis = {
            'failed_count': len(failed_examples),
            'total_count': len(train_pairs),
            'failure_rate': len(failed_examples) / len(train_pairs) if train_pairs else 1.0,
            'common_colors': [],
            'size_variations': [],
            'position_patterns': []
        }
        
        if failed_examples and train_pairs:
            # Analyser les couleurs communes dans les exemples échoués
            failed_colors = []
            for idx in failed_examples:
                if idx < len(train_pairs):
                    input_grid, output_grid = train_pairs[idx]
                    failed_colors.extend(np.unique(input_grid))
                    failed_colors.extend(np.unique(output_grid))
            
            analysis['common_colors'] = list(set(failed_colors))
            
            # Analyser les variations de taille
            for idx in failed_examples:
                if idx < len(train_pairs):
                    input_grid, output_grid = train_pairs[idx]
                    analysis['size_variations'].append({
                        'input_size': input_grid.shape,
                        'output_size': output_grid.shape
                    })
        
        return analysis
    
    def _adjust_color_parameter(self, 
                              param: ResolvedParameter,
                              failure_analysis: Dict[str, Any],
                              attempt: int) -> ResolvedParameter:
        """Ajuste un paramètre de couleur."""
        current_color = int(param.resolved_value) if param.resolved_value.isdigit() else 1
        
        # Stratégies d'ajustement selon la tentative
        if attempt == 1:
            # Essayer la couleur la plus commune dans les échecs
            if failure_analysis['common_colors']:
                new_color = failure_analysis['common_colors'][0]
            else:
                new_color = (current_color + 1) % 10
        elif attempt == 2:
            # Essayer une couleur différente
            new_color = (current_color + 2) % 10
        else:
            # Essayer aléatoirement parmi les couleurs communes
            if failure_analysis['common_colors']:
                import random
                new_color = random.choice(failure_analysis['common_colors'])
            else:
                new_color = (current_color + attempt) % 10
        
        adjusted_param = copy.deepcopy(param)
        adjusted_param.resolved_value = str(new_color)
        adjusted_param.confidence *= 0.8  # Réduire la confiance
        
        return adjusted_param
    
    def _adjust_pattern_parameter(self, 
                                param: ResolvedParameter,
                                failure_analysis: Dict[str, Any],
                                attempt: int) -> ResolvedParameter:
        """Ajuste un paramètre de pattern."""
        adjusted_param = copy.deepcopy(param)
        
        # Pour les patterns, essayer de réduire ou étendre la sélection
        current_pattern = param.resolved_value
        
        if attempt == 1:
            # Essayer de réduire le pattern (prendre moins de positions)
            if "(" in current_pattern and "[" in current_pattern:
                # Extraire les positions et en prendre la moitié
                positions = current_pattern.replace("(", "").replace(")", "").split()
                if len(positions) > 2:
                    reduced_positions = positions[:len(positions)//2]
                    adjusted_param.resolved_value = "(" + " ".join(reduced_positions) + ")"
        elif attempt == 2:
            # Essayer un pattern plus simple
            adjusted_param.resolved_value = "[0,0]"
        else:
            # Essayer un pattern étendu
            adjusted_param.resolved_value = "[0,0 2,2]"
        
        adjusted_param.confidence *= 0.7
        return adjusted_param
    
    def _adjust_position_parameter(self, 
                                 param: ResolvedParameter,
                                 failure_analysis: Dict[str, Any],
                                 attempt: int) -> ResolvedParameter:
        """Ajuste un paramètre de position."""
        adjusted_param = copy.deepcopy(param)
        
        # Extraire les coordonnées actuelles
        current_pos = param.resolved_value
        if "[" in current_pos and "," in current_pos:
            try:
                coords = current_pos.replace("[", "").replace("]", "").split(",")
                r, c = int(coords[0]), int(coords[1])
                
                # Ajuster selon la tentative
                if attempt == 1:
                    # Décaler légèrement
                    new_r, new_c = r + 1, c + 1
                elif attempt == 2:
                    # Essayer le centre
                    new_r, new_c = 1, 1
                else:
                    # Essayer une position aléatoire
                    new_r, new_c = attempt % 3, (attempt + 1) % 3
                
                adjusted_param.resolved_value = f"[{new_r},{new_c}]"
                
            except (ValueError, IndexError):
                # En cas d'erreur, utiliser une position par défaut
                adjusted_param.resolved_value = "[1,1]"
        
        adjusted_param.confidence *= 0.8
        return adjusted_param
    
    def _adjust_size_parameter(self, 
                             param: ResolvedParameter,
                             failure_analysis: Dict[str, Any],
                             attempt: int) -> ResolvedParameter:
        """Ajuste un paramètre de taille."""
        adjusted_param = copy.deepcopy(param)
        
        current_size = param.resolved_value
        if "x" in current_size:
            try:
                w, h = current_size.split("x")
                w, h = int(w), int(h)
                
                # Ajuster selon la tentative
                if attempt == 1:
                    # Essayer une taille légèrement différente
                    new_w, new_h = w + 1, h + 1
                elif attempt == 2:
                    # Essayer une taille plus petite
                    new_w, new_h = max(1, w - 1), max(1, h - 1)
                else:
                    # Utiliser les variations observées dans les échecs
                    if failure_analysis['size_variations']:
                        size_var = failure_analysis['size_variations'][0]
                        new_h, new_w = size_var['output_size']
                    else:
                        new_w, new_h = 3, 3  # Taille par défaut
                
                adjusted_param.resolved_value = f"{new_w}x{new_h}"
                
            except (ValueError, IndexError):
                adjusted_param.resolved_value = "3x3"
        
        adjusted_param.confidence *= 0.8
        return adjusted_param
    
    def _adjust_count_parameter(self, 
                              param: ResolvedParameter,
                              failure_analysis: Dict[str, Any],
                              attempt: int) -> ResolvedParameter:
        """Ajuste un paramètre de comptage."""
        adjusted_param = copy.deepcopy(param)
        
        current_count = int(param.resolved_value) if param.resolved_value.isdigit() else 1
        
        # Ajuster selon la tentative
        if attempt == 1:
            new_count = current_count + 1
        elif attempt == 2:
            new_count = max(1, current_count - 1)
        else:
            new_count = attempt % 5 + 1
        
        adjusted_param.resolved_value = str(new_count)
        adjusted_param.confidence *= 0.8
        
        return adjusted_param
    
    def _adjust_direction_parameter(self, 
                                  param: ResolvedParameter,
                                  failure_analysis: Dict[str, Any],
                                  attempt: int) -> ResolvedParameter:
        """Ajuste un paramètre de direction."""
        adjusted_param = copy.deepcopy(param)
        
        current_direction = param.resolved_value.upper()
        directions = ["HORIZONTAL", "VERTICAL", "LEFT", "RIGHT", "ABOVE", "BELOW"]
        
        # Essayer une direction différente
        if current_direction in directions:
            current_idx = directions.index(current_direction)
            new_idx = (current_idx + attempt) % len(directions)
            adjusted_param.resolved_value = directions[new_idx]
        else:
            adjusted_param.resolved_value = directions[attempt % len(directions)]
        
        adjusted_param.confidence *= 0.8
        return adjusted_param


class ValidationSystem:
    """
    Système de validation principal pour DINO-HRM-ARC.
    
    Coordonne la validation croisée, l'ajustement des paramètres,
    et les mécanismes de retry.
    """
    
    def __init__(self, max_retries: int = 3, success_threshold: float = 0.8):
        """
        Args:
            max_retries: Nombre maximum de tentatives
            success_threshold: Seuil de réussite (pourcentage d'exemples validés)
        """
        self.max_retries = max_retries
        self.success_threshold = success_threshold
        self.resolver = HRMParameterResolver()
        self.adjuster = ParameterAdjuster()
        self.analyzer = ParameterAnalyzer()
    
    def validate_solution(self, 
                         scenario: GeneralizedScenario,
                         test_input: np.ndarray,
                         train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> ValidationReport:
        """
        Valide une solution complète avec retry automatique.
        
        Args:
            scenario: Scénario généralisé à valider
            test_input: Grille de test
            train_pairs: Exemples d'entraînement pour validation
        
        Returns:
            Rapport de validation détaillé
        """
        import time
        start_time = time.time()
        
        best_report = None
        best_success_rate = 0.0
        
        for attempt in range(self.max_retries):
            print(f"\n=== Tentative de validation {attempt + 1}/{self.max_retries} ===")
            
            try:
                # Résoudre les paramètres
                if attempt == 0:
                    # Première tentative : résolution normale
                    resolved_params = self.resolver.resolve_parameters(
                        scenario, test_input, train_pairs
                    )
                else:
                    # Tentatives suivantes : ajuster les paramètres
                    resolved_params = self.adjuster.adjust_parameters(
                        resolved_params, best_report.failed_examples, train_pairs, attempt
                    )
                
                # Instancier le template
                concrete_command = self.resolver.instantiate_template(
                    scenario.template, resolved_params
                )
                
                print(f"Commande générée: {concrete_command}")
                
                # Valider sur tous les exemples train
                report = self._validate_on_all_examples(
                    concrete_command, train_pairs, test_input
                )
                
                # Mettre à jour le meilleur résultat
                if report.success_rate > best_success_rate:
                    best_success_rate = report.success_rate
                    best_report = report
                    best_report.final_command = concrete_command
                
                # Vérifier si on a atteint le seuil de succès
                if report.success_rate >= self.success_threshold:
                    print(f"✓ Validation réussie avec {report.success_rate:.1%} de succès")
                    best_report.result = ValidationResult.SUCCESS
                    break
                else:
                    print(f"⚠ Validation partielle: {report.success_rate:.1%} de succès")
                    
            except Exception as e:
                print(f"✗ Erreur lors de la tentative {attempt + 1}: {e}")
                if best_report is None:
                    best_report = ValidationReport(
                        result=ValidationResult.ERROR,
                        success_rate=0.0,
                        successful_examples=0,
                        total_examples=len(train_pairs),
                        failed_examples=list(range(len(train_pairs))),
                        error_messages=[str(e)]
                    )
        
        # Finaliser le rapport
        if best_report is None:
            best_report = ValidationReport(
                result=ValidationResult.FAILURE,
                success_rate=0.0,
                successful_examples=0,
                total_examples=len(train_pairs),
                failed_examples=list(range(len(train_pairs))),
                error_messages=["Aucune tentative n'a réussi"]
            )
        
        # Déterminer le résultat final
        if best_report.result != ValidationResult.SUCCESS:
            if best_success_rate >= 0.5:
                best_report.result = ValidationResult.PARTIAL_SUCCESS
            elif best_success_rate > 0:
                best_report.result = ValidationResult.FAILURE
            # ERROR reste ERROR
        
        best_report.execution_time = time.time() - start_time
        
        print(f"\n=== Résultat final ===")
        print(f"Statut: {best_report.result.value}")
        print(f"Taux de succès: {best_report.success_rate:.1%}")
        print(f"Exemples réussis: {best_report.successful_examples}/{best_report.total_examples}")
        print(f"Temps d'exécution: {best_report.execution_time:.2f}s")
        
        return best_report
    
    def _validate_on_all_examples(self, 
                                 concrete_command: str,
                                 train_pairs: List[Tuple[np.ndarray, np.ndarray]],
                                 test_input: Optional[np.ndarray] = None) -> ValidationReport:
        """
        Valide une commande sur tous les exemples d'entraînement.
        
        Args:
            concrete_command: Commande concrète à valider
            train_pairs: Exemples (input, expected_output)
            test_input: Grille de test (pour le résultat final)
        
        Returns:
            Rapport de validation
        """
        successful_examples = 0
        failed_examples = []
        error_messages = []
        final_result = None
        
        # Valider sur chaque exemple train
        for i, (input_grid, expected_output) in enumerate(train_pairs):
            try:
                # Exécuter la commande sur cet exemple
                result, validation_success = self.resolver.execute_and_validate(
                    concrete_command, input_grid, [(input_grid, expected_output)]
                )
                
                if validation_success and result is not None:
                    successful_examples += 1
                    print(f"  ✓ Exemple {i+1}: Validé")
                else:
                    failed_examples.append(i)
                    print(f"  ✗ Exemple {i+1}: Échec")
                    
            except Exception as e:
                failed_examples.append(i)
                error_messages.append(f"Exemple {i+1}: {str(e)}")
                print(f"  ✗ Exemple {i+1}: Erreur - {e}")
        
        # Exécuter sur test_input pour le résultat final
        if test_input is not None:
            try:
                final_result, _ = self.resolver.execute_and_validate(
                    concrete_command, test_input, train_pairs
                )
            except Exception as e:
                error_messages.append(f"Test input: {str(e)}")
        
        # Calculer le taux de succès
        success_rate = successful_examples / len(train_pairs) if train_pairs else 0.0
        
        return ValidationReport(
            result=ValidationResult.SUCCESS if success_rate >= self.success_threshold else ValidationResult.FAILURE,
            success_rate=success_rate,
            successful_examples=successful_examples,
            total_examples=len(train_pairs),
            failed_examples=failed_examples,
            error_messages=error_messages,
            final_result=final_result
        )
    
    def quick_validate(self, 
                      concrete_command: str,
                      train_pairs: List[Tuple[np.ndarray, np.ndarray]]) -> bool:
        """
        Validation rapide sans retry pour tests préliminaires.
        
        Args:
            concrete_command: Commande à valider
            train_pairs: Exemples d'entraînement
        
        Returns:
            True si la validation passe le seuil minimum
        """
        try:
            report = self._validate_on_all_examples(concrete_command, train_pairs)
            return report.success_rate >= 0.5  # Seuil plus bas pour validation rapide
        except Exception:
            return False
    
    def get_validation_statistics(self, 
                                 reports: List[ValidationReport]) -> Dict[str, Any]:
        """
        Calcule des statistiques sur plusieurs rapports de validation.
        
        Args:
            reports: Liste de rapports de validation
        
        Returns:
            Statistiques agrégées
        """
        if not reports:
            return {}
        
        success_rates = [r.success_rate for r in reports]
        execution_times = [r.execution_time for r in reports]
        
        stats = {
            'total_validations': len(reports),
            'successful_validations': sum(1 for r in reports if r.result == ValidationResult.SUCCESS),
            'partial_validations': sum(1 for r in reports if r.result == ValidationResult.PARTIAL_SUCCESS),
            'failed_validations': sum(1 for r in reports if r.result == ValidationResult.FAILURE),
            'error_validations': sum(1 for r in reports if r.result == ValidationResult.ERROR),
            'average_success_rate': np.mean(success_rates),
            'median_success_rate': np.median(success_rates),
            'min_success_rate': np.min(success_rates),
            'max_success_rate': np.max(success_rates),
            'average_execution_time': np.mean(execution_times),
            'total_execution_time': np.sum(execution_times)
        }
        
        return stats