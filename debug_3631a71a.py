import numpy as np
import json

# Charger la tâche 3631a71a
with open("arcdata/training/3631a71a.json", 'r', encoding='utf-8') as f:
    task_data = json.load(f)

test_input = np.array(task_data['test'][0]['input'])

# Vérifier les rectangles suspects pour la valeur 9
print("Vérification des rectangles de valeur 9:")

# Rectangle [3,22 11,24]
print("\nRectangle [3,22 11,24]:")
rect1 = test_input[3:12, 22:25]
print("Contenu:", rect1)
print("Toutes les valeurs sont 9?", np.all(rect1 == 9))
print("Valeurs uniques:", np.unique(rect1))

# Rectangle [7,20 7,26] 
print("\nRectangle [7,20 7,26]:")
rect2 = test_input[7:8, 20:27]
print("Contenu:", rect2)
print("Toutes les valeurs sont 9?", np.all(rect2 == 9))
print("Valeurs uniques:", np.unique(rect2))

# Afficher une partie de la grille pour voir la distribution de la valeur 9
print("\nPartie de la grille (lignes 3-12, colonnes 20-26):")
print(test_input[3:13, 20:27])