import numpy as np
import json
import os
import re

def all_rectangles(mat, background_colors=None, prioritize_uniform=False):
    mat = np.asarray(mat)
    results = {}
    
    # Si pas de couleurs de fond spécifiées, prendre celle avec le plus de cellules
    if background_colors is None:
        unique_values, counts = np.unique(mat, return_counts=True)
        background_colors = [unique_values[np.argmax(counts)]]
    elif not isinstance(background_colors, list):
        background_colors = [background_colors]
    
    for val in np.unique(mat):
        # Ignorer les couleurs de fond
        if val in background_colors:
            continue
            
        rects = []
        for r1 in range(mat.shape[0]):
            for c1 in range(mat.shape[1]):
                for r2 in range(r1, mat.shape[0]):
                    for c2 in range(c1, mat.shape[1]):
                        rect_size = (r2 - r1 + 1) * (c2 - c1 + 1)
                        if rect_size > 4:
                            rect_region = mat[r1:r2+1, c1:c2+1]
                            if prioritize_uniform:
                                # Pour dc0a314f: privilégier les rectangles uniformes (masque complet)
                                if np.all(rect_region == val):
                                    rects.append((r1, c1, r2, c2))
                            else:
                                # Comportement normal
                                if np.all(rect_region == val):
                                    rects.append((r1, c1, r2, c2))
        if rects:
            merged_rects = merge_overlapping_rectangles(rects)
            results[int(val)] = [(r1, c1, r2, c2) for r1, c1, r2, c2 in merged_rects]
    
    # Si prioritize_uniform, garder seulement la couleur avec le plus de rectangles uniformes
    if prioritize_uniform and results:
        best_color = max(results.keys(), key=lambda k: len(results[k]))
        results = {best_color: results[best_color]}
    
    return results

def all_rectangles_conservative(mat):
    """Version qui privilégie les rectangles de taille optimale sans sur-fusion"""
    mat = np.asarray(mat)
    results = {}
    
    # Trouver la couleur de fond (celle avec le plus de cellules)
    unique_values, counts = np.unique(mat, return_counts=True)
    background_color = unique_values[np.argmax(counts)]
    
    for val in np.unique(mat):
        # Ignorer la couleur de fond
        if val == background_color:
            continue
            
        rects = []
        for r1 in range(mat.shape[0]):
            for c1 in range(mat.shape[1]):
                for r2 in range(r1, mat.shape[0]):
                    for c2 in range(c1, mat.shape[1]):
                        rect_size = (r2 - r1 + 1) * (c2 - c1 + 1)
                        if np.all(mat[r1:r2+1, c1:c2+1] == val) and rect_size > 4:
                            rects.append((r1, c1, r2, c2))
        
        if rects:
            # Sélectionner les rectangles optimaux (éviter les doublons et privilégier les plus grands)
            optimal_rects = select_optimal_rectangles(rects)
            results[int(val)] = optimal_rects
    return results

def select_optimal_rectangles(rectangles):
    """Sélectionne les rectangles optimaux en évitant les redondances"""
    if not rectangles:
        return []
    
    # Trier par taille décroissante
    rectangles = sorted(rectangles, key=lambda r: (r[2]-r[0]+1)*(r[3]-r[1]+1), reverse=True)
    
    selected = []
    for rect in rectangles:
        # Vérifier si ce rectangle est déjà couvert par un rectangle sélectionné
        is_covered = False
        for selected_rect in selected:
            if rectangle_contains(selected_rect, rect):
                is_covered = True
                break
        
        if not is_covered:
            selected.append(rect)
    
    return selected

def rectangle_contains(rect1, rect2):
    """Vérifie si rect1 contient complètement rect2"""
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    
    return (r1_1 <= r1_2 and c1_1 <= c1_2 and 
            r2_1 >= r2_2 and c2_1 >= c2_2)

def find_best_background_color(task_data):
    """Trouve la couleur de fond qui donne le plus de rectangles sur les exemples d'entraînement"""
    if 'train' not in task_data:
        return None
    
    color_scores = {}
    
    for example in task_data['train']:
        input_grid = np.array(example['input'])
        unique_colors = np.unique(input_grid)
        
        for bg_color in unique_colors:
            rectangles = all_rectangles(input_grid, bg_color)
            total_rects = sum(len(rects) for rects in rectangles.values())
            
            if bg_color not in color_scores:
                color_scores[bg_color] = 0
            color_scores[bg_color] += total_rects
    
    if color_scores:
        best_bg_color = max(color_scores.keys(), key=lambda k: color_scores[k])
        return best_bg_color
    
    return None

def rectangles_overlap(rect1, rect2):
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    return not (r2_1 < r1_2 or r2_2 < r1_1 or c2_1 < c1_2 or c2_2 < c1_1)

def merge_two_rectangles(rect1, rect2):
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    return (min(r1_1, r1_2), min(c1_1, c1_2), max(r2_1, r2_2), max(c2_1, c2_2))

def merge_overlapping_rectangles(rectangles, merge_threshold=0.5):
    """
    Fusionne les rectangles qui se chevauchent significativement.
    merge_threshold: seuil de chevauchement pour décider de fusionner (0.5 = 50%)
    """
    if not rectangles:
        return []
    
    # Pour les puzzles mosaïques, être plus conservateur dans la fusion
    # Ne fusionner que si le chevauchement est très important
    merged = []
    used = set()
    
    for i, rect1 in enumerate(rectangles):
        if i in used:
            continue
        
        current_merged = rect1
        overlapping_indices = {i}
        
        # Chercher les rectangles avec chevauchement significatif
        for j, rect2 in enumerate(rectangles):
            if j != i and j not in used:
                if significant_overlap(current_merged, rect2, merge_threshold):
                    current_merged = merge_two_rectangles(current_merged, rect2)
                    overlapping_indices.add(j)
        
        merged.append(current_merged)
        used.update(overlapping_indices)
    
    return merged

def significant_overlap(rect1, rect2, threshold=0.5):
    """Vérifie si deux rectangles ont un chevauchement significatif"""
    if not rectangles_overlap(rect1, rect2):
        return False
    
    r1_1, c1_1, r2_1, c2_1 = rect1
    r1_2, c1_2, r2_2, c2_2 = rect2
    
    # Calculer l'aire de chevauchement
    overlap_r1 = max(r1_1, r1_2)
    overlap_c1 = max(c1_1, c1_2)
    overlap_r2 = min(r2_1, r2_2)
    overlap_c2 = min(c2_1, c2_2)
    
    if overlap_r1 <= overlap_r2 and overlap_c1 <= overlap_c2:
        overlap_area = (overlap_r2 - overlap_r1 + 1) * (overlap_c2 - overlap_c1 + 1)
        
        # Aires des rectangles
        area1 = (r2_1 - r1_1 + 1) * (c2_1 - c1_1 + 1)
        area2 = (r2_2 - r1_2 + 1) * (c2_2 - c1_2 + 1)
        
        # Pourcentage de chevauchement par rapport au plus petit rectangle
        min_area = min(area1, area2)
        overlap_ratio = overlap_area / min_area
        
        return overlap_ratio >= threshold
    
    return False

def get_agi_commands(agi_content):
    """Extrait les commandes brutes du fichier AGI sans interprétation"""
    lines = agi_content.strip().split('\n')
    commands = []
    
    for line in lines:
        line = line.strip()
        # Ignorer les lignes TRANSFERT, INIT et END
        if line.startswith('TRANSFERT') or line.startswith('INIT') or line == 'END':
            continue
        if line:  # Ignorer les lignes vides
            commands.append(line)
    
    return commands

def load_arc_data():
    training_path = "arcdata/training"
    data = {}
    
    if os.path.exists(training_path):
        for filename in os.listdir(training_path):
            if filename.endswith('.json'):
                task_id = filename[:-5]
                try:
                    with open(os.path.join(training_path, filename), 'r', encoding='utf-8') as f:
                        data[task_id] = json.load(f)
                except:
                    pass
    
    return data

def compare_coordinates():
    # Les 8 vrais puzzles mosaïques
    true_mosaics = ['0dfd9992', '29ec7d0e', '3631a71a', '484b58aa', '9ecd008a', 'b8825c91', 'c3f564a4', 'dc0a314f']
    
    arc_data = load_arc_data()
    training_path = "arcdata/training"
    
    print("=== COMPARAISON COORDONNÉES DÉTECTÉES VS FICHIERS AGI ===\n")
    
    for task_id in true_mosaics:
        if task_id not in arc_data:
            continue
        
        print(f"--- {task_id} ---")
        
        # Charger le fichier AGI
        agi_file = os.path.join(training_path, f"{task_id}_TEST0_VALID.agi")
        if not os.path.exists(agi_file):
            print("Fichier AGI non trouvé")
            continue
        
        try:
            with open(agi_file, 'r', encoding='utf-8') as f:
                agi_content = f.read()
        except:
            print("Erreur lecture fichier AGI")
            continue
        
        # Extraire les commandes brutes du fichier AGI
        agi_commands = get_agi_commands(agi_content)
        print(f"Commandes AGI:")
        for cmd in agi_commands:
            print(f"  {cmd}")
        
        # Analyser le test input pour trouver les coordonnées détectées
        task_data = arc_data[task_id]
        if 'test' in task_data and len(task_data['test']) > 0:
            test_input = np.array(task_data['test'][0]['input'])
            
            # Cas spéciaux
            if task_id == 'c3f564a4':
                # Fond multi-couleurs (1,2,3,4,5,6,7,8) avec répartition uniforme, exclure 0
                background_colors = [1, 2, 3, 4, 5, 6, 7, 8]
                print(f"Couleurs de fond pour {task_id}: {background_colors}")
                detected_rectangles = all_rectangles(test_input, background_colors)
            elif task_id == 'dc0a314f':
                # Privilégier les rectangles avec masque complet (uniformes)
                print(f"Mode masque complet pour {task_id}")
                detected_rectangles = all_rectangles(test_input, prioritize_uniform=True)
            elif task_id in ['3631a71a', '29ec7d0e']:
                # Pour les mosaïques complexes, être plus conservateur dans la fusion
                print(f"Mode fusion conservatrice pour {task_id}")
                detected_rectangles = all_rectangles_conservative(test_input)
            else:
                detected_rectangles = all_rectangles(test_input)
            
            # Afficher la répartition des couleurs
            unique_values, counts = np.unique(test_input, return_counts=True)
            print(f"Répartition des couleurs:")
            for val, count in zip(unique_values, counts):
                percentage = count / test_input.size * 100
                print(f"  Couleur {val}: {count} pixels ({percentage:.1f}%)")
            
            print(f"Rectangles détectés:")
            for color, rects in detected_rectangles.items():
                rects_formatted = [f"[{r1},{c1} {r2},{c2}]" for r1, c1, r2, c2 in rects]
                print(f"  Couleur {color}: {rects_formatted}")
            
            # Afficher les rectangles détectés
            print(f"Rectangles détectés: {len(sum(detected_rectangles.values(), []))} au total")
            
            # Comparaison avec les commandes AGI pour les cas spéciaux
            if task_id == '3631a71a':
                expected = ['[27,18 28,24]', '[2,0 4,6]', '[3,20 7,24]', '[7,22 11,26]']
                all_detected_formatted = []
                for color, rects in detected_rectangles.items():
                    for r1, c1, r2, c2 in rects:
                        all_detected_formatted.append(f"[{r1},{c1} {r2},{c2}]")
                
                print(f"Attendu: {expected}")
                print(f"Détecté: {all_detected_formatted}")
                matches = sum(1 for exp in expected if exp in all_detected_formatted)
                print(f"Correspondances: {matches}/{len(expected)}")
        
        print()

if __name__ == "__main__":
    compare_coordinates()