# Design Document

## Overview

Architecture à deux étages pour résoudre les puzzles ARC : DINO pour l'analyse et la catégorisation, HRM pour l'exécution et la validation. Le système reproduit le processus cognitif humain d'analyse comparative puis d'application de règles.

## Architecture

```
puzzle.json → DINO-ARC → category + generalized_scenario → HRM → test_output
```

### Flux de données principal

1. **Input** : Puzzle JSON avec multiples exemples train
2. **DINO Analysis** : Comparaison multi-exemples → détection patterns → catégorisation
3. **Scenario Generation** : Template généralisé avec paramètres variables
4. **HRM Resolution** : Instanciation paramètres → exécution → validation
5. **Output** : Solution pour test_input

## Components and Interfaces

### MultiExampleDataset
**Fichier** : `src/multi_example_dataset.py`
**Responsabilité** : Charger tous les exemples train d'un puzzle

```python
class MultiExampleDataset(Dataset):
    def __getitem__(self, idx) -> Dict:
        return {
            "train_pairs": List[Tuple[grid, grid]],
            "test_input": grid,
            "puzzle_id": str
        }
```

### DINO-ARC
**Fichier** : `models/dino_arc.py`
**Responsabilité** : Analyse multi-exemples et catégorisation

```python
class DinoARC(nn.Module):
    def forward(self, train_pairs) -> Dict:
        return {
            "category": str,
            "confidence": float,
            "pattern_features": Tensor
        }
```

### ScenarioGeneralizer
**Fichier** : `src/scenario_generalizer.py`
**Responsabilité** : Créer templates généralisés à partir de catégories

```python
class ScenarioGeneralizer:
    def generate_scenario(self, category: str) -> Dict:
        return {
            "template": str,  # "EDIT {color_var} {pattern_var}"
            "constraints": Dict  # {"color_var": "most_frequent"}
        }
```

### HRMParameterResolver
**Fichier** : `models/hrm_parameter_resolver.py`
**Responsabilité** : Résoudre paramètres et exécuter commandes

```python
class HRMParameterResolver:
    def resolve_and_execute(self, scenario, test_input, train_pairs) -> grid:
        parameters = self.resolve_parameters(scenario, test_input)
        concrete_commands = self.instantiate(scenario["template"], parameters)
        result = self.execute(concrete_commands, test_input)
        if self.validate(result, train_pairs):
            return result
        else:
            return self.adjust_and_retry(scenario, test_input, train_pairs)
```

### ARCSolver (Pipeline principal)
**Fichier** : `models/arc_solver.py`
**Responsabilité** : Orchestrer DINO → HRM

```python
class ARCSolver:
    def solve_puzzle(self, puzzle_data) -> grid:
        # 1. Analyse DINO
        analysis = self.dino.forward(puzzle_data["train_pairs"])
        
        # 2. Génération scénario
        scenario = self.generalizer.generate_scenario(analysis["category"])
        
        # 3. Résolution HRM
        solution = self.hrm.resolve_and_execute(
            scenario, 
            puzzle_data["test_input"], 
            puzzle_data["train_pairs"]
        )
        
        return solution
```

## Data Models

### PuzzleData
```python
@dataclass
class PuzzleData:
    train_pairs: List[Tuple[np.ndarray, np.ndarray]]
    test_input: np.ndarray
    puzzle_id: str
```

### AnalysisResult
```python
@dataclass
class AnalysisResult:
    category: str
    confidence: float
    pattern_features: torch.Tensor
```

### GeneralizedScenario
```python
@dataclass
class GeneralizedScenario:
    template: str  # "EDIT {color_var} {pattern_var}"
    constraints: Dict[str, Any]  # {"color_var": "most_frequent_color"}
    category: str
```

## Error Handling

### DINO Errors
- **Low confidence** : Retourner catégorie "unknown" avec template générique
- **No pattern detected** : Utiliser template de fallback basique

### HRM Errors
- **Parameter resolution failure** : Essayer contraintes alternatives
- **Validation failure** : Ajuster paramètres et réessayer (max 3 tentatives)
- **Execution error** : Retourner grille vide avec flag d'erreur

### Pipeline Errors
- **Invalid puzzle format** : Lever exception avec message explicite
- **Missing train examples** : Utiliser mode dégradé avec exemple unique

## Testing Strategy

### Unit Tests
- Chaque composant testé individuellement avec données synthétiques
- Validation des interfaces entre composants
- Tests de cas d'erreur et de récupération

### Integration Tests
- Pipeline complet sur puzzles ARC simples connus
- Validation end-to-end sur échantillon représentatif
- Tests de performance et de temps de réponse

### Validation Tests
- Comparaison avec solutions AGI existantes
- Métriques de précision par catégorie de puzzle
- Tests de robustesse sur puzzles non vus