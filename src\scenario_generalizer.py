"""
Généralisateur de scénarios pour DINO-HRM-ARC.

Transforme les commandes AGI spécifiques en templates généralisés avec paramètres variables.
Crée le mapping catégorie → template pour la résolution par HRM.
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class ParameterType(Enum):
    """Types de paramètres variables dans les templates."""
    COLOR = "color"
    POSITION = "position"
    SIZE = "size"
    DIRECTION = "direction"
    PATTERN = "pattern"
    COUNT = "count"


@dataclass
class ParameterConstraint:
    """Contrainte pour la résolution d'un paramètre."""
    param_name: str
    param_type: ParameterType
    constraint_type: str  # "most_frequent", "dominant_color", "center_position", etc.
    constraint_data: Any = None  # Données additionnelles pour la contrainte


@dataclass
class GeneralizedScenario:
    """Scénario généralisé avec template et contraintes."""
    template: str  # Template avec variables : "EDIT {color_var} {pattern_var}"
    constraints: List[ParameterConstraint]  # Contraintes pour résoudre les paramètres
    category: str  # Catégorie du puzzle
    confidence: float = 1.0  # Confiance dans le template


class ScenarioGeneralizer:
    """
    Généralisateur de scénarios AGI.
    
    Transforme les commandes spécifiques en templates généralisés
    et définit les contraintes de résolution des paramètres.
    """
    
    def __init__(self):
        """Initialise le généralisateur avec les templates de base."""
        self.category_templates = self._initialize_templates()
        
    def _initialize_templates(self) -> Dict[str, GeneralizedScenario]:
        """Initialise les templates pour chaque catégorie de puzzle ARC."""
        templates = {}
        
        # Template pour changement de couleur
        templates['color_change'] = GeneralizedScenario(
            template="EDIT {target_color} {selection_pattern}",
            constraints=[
                ParameterConstraint("target_color", ParameterType.COLOR, "most_frequent_output"),
                ParameterConstraint("selection_pattern", ParameterType.PATTERN, "input_pattern_match")
            ],
            category='color_change'
        )
        
        # Template pour complétion de forme
        templates['shape_completion'] = GeneralizedScenario(
            template="FILL {fill_color} {completion_area}",
            constraints=[
                ParameterConstraint("fill_color", ParameterType.COLOR, "dominant_color"),
                ParameterConstraint("completion_area", ParameterType.PATTERN, "incomplete_shape_detection")
            ],
            category='shape_completion'
        )
        
        # Template pour répétition de motif
        templates['pattern_repetition'] = GeneralizedScenario(
            template="MOTIF {COPY {source_pattern}; MULTIPLY {repeat_count} {repeat_direction} {target_area}}",
            constraints=[
                ParameterConstraint("source_pattern", ParameterType.PATTERN, "smallest_repeating_unit"),
                ParameterConstraint("repeat_count", ParameterType.COUNT, "pattern_frequency"),
                ParameterConstraint("repeat_direction", ParameterType.DIRECTION, "repetition_axis"),
                ParameterConstraint("target_area", ParameterType.PATTERN, "full_grid")
            ],
            category='pattern_repetition'
        )
        
        # Template pour symétrie
        templates['symmetry'] = GeneralizedScenario(
            template="MOTIF {COPY {source_half}; FLIP {flip_axis} {target_half}}",
            constraints=[
                ParameterConstraint("source_half", ParameterType.PATTERN, "non_empty_half"),
                ParameterConstraint("flip_axis", ParameterType.DIRECTION, "symmetry_axis"),
                ParameterConstraint("target_half", ParameterType.PATTERN, "empty_half")
            ],
            category='symmetry'
        )
        
        # Template pour rotation
        templates['rotation'] = GeneralizedScenario(
            template="ROTATE {rotation_angle} {rotation_center}",
            constraints=[
                ParameterConstraint("rotation_angle", ParameterType.DIRECTION, "rotation_detection"),
                ParameterConstraint("rotation_center", ParameterType.POSITION, "geometric_center")
            ],
            category='rotation'
        )
        
        # Template pour mise à l'échelle
        templates['scaling'] = GeneralizedScenario(
            template="MOTIF {COPY {source_object}; MULTIPLY {scale_factor} true {target_area}}",
            constraints=[
                ParameterConstraint("source_object", ParameterType.PATTERN, "main_object"),
                ParameterConstraint("scale_factor", ParameterType.COUNT, "size_ratio"),
                ParameterConstraint("target_area", ParameterType.PATTERN, "scaled_position")
            ],
            category='scaling'
        )
        
        # Template pour translation
        templates['translation'] = GeneralizedScenario(
            template="MOTIF {COPY {source_object}; PASTE {target_position}}",
            constraints=[
                ParameterConstraint("source_object", ParameterType.PATTERN, "moving_object"),
                ParameterConstraint("target_position", ParameterType.POSITION, "translation_vector")
            ],
            category='translation'
        )
        
        # Template pour comptage d'objets
        templates['object_counting'] = GeneralizedScenario(
            template="EDIT {count_color} {count_positions}",
            constraints=[
                ParameterConstraint("count_color", ParameterType.COLOR, "counting_result_color"),
                ParameterConstraint("count_positions", ParameterType.PATTERN, "object_count_encoding")
            ],
            category='object_counting'
        )
        
        # Template pour dessin de ligne
        templates['line_drawing'] = GeneralizedScenario(
            template="EDIT {line_color} {line_path}",
            constraints=[
                ParameterConstraint("line_color", ParameterType.COLOR, "line_color_rule"),
                ParameterConstraint("line_path", ParameterType.PATTERN, "connection_path")
            ],
            category='line_drawing'
        )
        
        # Template pour remplissage de rectangle
        templates['rectangle_filling'] = GeneralizedScenario(
            template="FILL {fill_color} {rectangle_area}",
            constraints=[
                ParameterConstraint("fill_color", ParameterType.COLOR, "fill_rule"),
                ParameterConstraint("rectangle_area", ParameterType.PATTERN, "rectangle_detection")
            ],
            category='rectangle_filling'
        )
        
        # Template pour motif mosaïque
        templates['mosaic_pattern'] = GeneralizedScenario(
            template="MOTIF {COPY {tile_pattern}; MULTIPLY {tile_count} true {grid_area}}",
            constraints=[
                ParameterConstraint("tile_pattern", ParameterType.PATTERN, "base_tile"),
                ParameterConstraint("tile_count", ParameterType.COUNT, "mosaic_repetition"),
                ParameterConstraint("grid_area", ParameterType.PATTERN, "full_grid")
            ],
            category='mosaic_pattern'
        )
        
        # Template pour transformation de grille
        templates['grid_transformation'] = GeneralizedScenario(
            template="RESIZE {new_size}; MOTIF {COPY {source_content}; PASTE {target_layout}}",
            constraints=[
                ParameterConstraint("new_size", ParameterType.SIZE, "output_size"),
                ParameterConstraint("source_content", ParameterType.PATTERN, "content_extraction"),
                ParameterConstraint("target_layout", ParameterType.PATTERN, "layout_rule")
            ],
            category='grid_transformation'
        )
        
        # Template pour mapping de couleur
        templates['color_mapping'] = GeneralizedScenario(
            template="REPLACE {old_color} {new_color}",
            constraints=[
                ParameterConstraint("old_color", ParameterType.COLOR, "source_color_rule"),
                ParameterConstraint("new_color", ParameterType.COLOR, "target_color_rule")
            ],
            category='color_mapping'
        )
        
        # Template pour extraction de forme
        templates['shape_extraction'] = GeneralizedScenario(
            template="MOTIF {COPY {extracted_shape}; CLEAR {background}; PASTE {center_position}}",
            constraints=[
                ParameterConstraint("extracted_shape", ParameterType.PATTERN, "main_shape"),
                ParameterConstraint("background", ParameterType.PATTERN, "background_area"),
                ParameterConstraint("center_position", ParameterType.POSITION, "center_placement")
            ],
            category='shape_extraction'
        )
        
        # Template pour superposition de motifs
        templates['pattern_overlay'] = GeneralizedScenario(
            template="MOTIF {COPY {base_pattern}; COPY {overlay_pattern}; PASTE {overlay_positions}}",
            constraints=[
                ParameterConstraint("base_pattern", ParameterType.PATTERN, "background_pattern"),
                ParameterConstraint("overlay_pattern", ParameterType.PATTERN, "foreground_pattern"),
                ParameterConstraint("overlay_positions", ParameterType.PATTERN, "overlay_rule")
            ],
            category='pattern_overlay'
        )
        
        # Template pour règle géométrique
        templates['geometric_rule'] = GeneralizedScenario(
            template="EDIT {result_color} {geometric_positions}",
            constraints=[
                ParameterConstraint("result_color", ParameterType.COLOR, "geometric_result"),
                ParameterConstraint("geometric_positions", ParameterType.PATTERN, "geometric_calculation")
            ],
            category='geometric_rule'
        )
        
        # Template pour remplissage conditionnel
        templates['conditional_fill'] = GeneralizedScenario(
            template="FILL {conditional_color} {conditional_area}",
            constraints=[
                ParameterConstraint("conditional_color", ParameterType.COLOR, "condition_result"),
                ParameterConstraint("conditional_area", ParameterType.PATTERN, "condition_check")
            ],
            category='conditional_fill'
        )
        
        # Template pour détection de frontière
        templates['boundary_detection'] = GeneralizedScenario(
            template="EDIT {boundary_color} {boundary_positions}",
            constraints=[
                ParameterConstraint("boundary_color", ParameterType.COLOR, "boundary_marker"),
                ParameterConstraint("boundary_positions", ParameterType.PATTERN, "edge_detection")
            ],
            category='boundary_detection'
        )
        
        # Template pour correspondance de template
        templates['template_match'] = GeneralizedScenario(
            template="MOTIF {COPY {template_pattern}; PASTE {match_positions}}",
            constraints=[
                ParameterConstraint("template_pattern", ParameterType.PATTERN, "reference_template"),
                ParameterConstraint("match_positions", ParameterType.PATTERN, "template_matching")
            ],
            category='template_match'
        )
        
        # Template par défaut pour puzzles non catégorisés
        templates['unknown'] = GeneralizedScenario(
            template="TRANSFERT",
            constraints=[],
            category='unknown'
        )
        
        return templates
    
    def generate_scenario(self, category: str, confidence: float = 1.0) -> GeneralizedScenario:
        """
        Génère un scénario généralisé pour une catégorie donnée.
        
        Args:
            category: Catégorie du puzzle (détectée par DINO)
            confidence: Confiance dans la catégorisation
        
        Returns:
            GeneralizedScenario avec template et contraintes
        """
        if category not in self.category_templates:
            print(f"ATTENTION: Catégorie inconnue '{category}', utilisation du template par défaut")
            category = 'unknown'
        
        scenario = self.category_templates[category]
        scenario.confidence = confidence
        
        return scenario
    
    def generalize_specific_program(self, program: str) -> GeneralizedScenario:
        """
        Généralise un programme AGI spécifique en template avec variables.
        
        Args:
            program: Programme AGI spécifique (ex: "EDIT 3 ([1,1] [2,2])")
        
        Returns:
            GeneralizedScenario avec template généralisé
        """
        # Analyser le programme pour détecter les patterns
        generalized_template = program
        constraints = []
        
        # Remplacer les couleurs spécifiques par des variables
        color_pattern = r'\b([0-9])\b'
        colors_found = re.findall(color_pattern, program)
        
        for i, color in enumerate(set(colors_found)):
            var_name = f"color_var_{i}"
            generalized_template = re.sub(rf'\b{color}\b', f'{{{var_name}}}', generalized_template)
            constraints.append(
                ParameterConstraint(var_name, ParameterType.COLOR, "specific_color", int(color))
            )
        
        # Remplacer les coordonnées spécifiques par des variables de pattern
        coord_pattern = r'\[([0-9]+),([0-9]+)\]'
        coords_found = re.findall(coord_pattern, program)
        
        if coords_found:
            var_name = "position_pattern"
            generalized_template = re.sub(coord_pattern, f'{{{var_name}}}', generalized_template)
            constraints.append(
                ParameterConstraint(var_name, ParameterType.PATTERN, "coordinate_list", coords_found)
            )
        
        # Remplacer les tailles spécifiques
        size_pattern = r'([0-9]+)x([0-9]+)'
        sizes_found = re.findall(size_pattern, program)
        
        for i, (w, h) in enumerate(sizes_found):
            var_name = f"size_var_{i}"
            generalized_template = re.sub(rf'{w}x{h}', f'{{{var_name}}}', generalized_template)
            constraints.append(
                ParameterConstraint(var_name, ParameterType.SIZE, "specific_size", (int(w), int(h)))
            )
        
        return GeneralizedScenario(
            template=generalized_template,
            constraints=constraints,
            category='generalized_from_program'
        )
    
    def get_available_categories(self) -> List[str]:
        """Retourne la liste des catégories disponibles."""
        return list(self.category_templates.keys())
    
    def validate_scenario(self, scenario: GeneralizedScenario) -> Dict[str, Any]:
        """
        Valide un scénario généralisé.
        
        Args:
            scenario: Scénario à valider
        
        Returns:
            Dict avec résultat de validation
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Vérifier que toutes les variables du template ont des contraintes
        template_vars = re.findall(r'\{([^}]+)\}', scenario.template)
        constraint_vars = {c.param_name for c in scenario.constraints}
        
        missing_constraints = set(template_vars) - constraint_vars
        if missing_constraints:
            validation_result['errors'].append(
                f"Variables sans contraintes: {missing_constraints}"
            )
            validation_result['is_valid'] = False
        
        # Vérifier que les contraintes sont cohérentes
        for constraint in scenario.constraints:
            if constraint.param_name not in template_vars:
                validation_result['warnings'].append(
                    f"Contrainte orpheline: {constraint.param_name}"
                )
        
        # Vérifier la syntaxe du template
        if not self._is_valid_agi_syntax(scenario.template):
            validation_result['errors'].append("Syntaxe AGI invalide dans le template")
            validation_result['is_valid'] = False
        
        return validation_result
    
    def _is_valid_agi_syntax(self, template: str) -> bool:
        """
        Vérifie la syntaxe AGI d'un template (basique).
        
        Args:
            template: Template à vérifier
        
        Returns:
            True si la syntaxe semble valide
        """
        # Commandes AGI valides
        valid_commands = [
            'EDIT', 'FILL', 'CLEAR', 'REPLACE', 'COPY', 'PASTE', 'CUT',
            'FLIP', 'ROTATE', 'RESIZE', 'TRANSFERT', 'MOTIF', 'INIT',
            'MULTIPLY', 'DIVIDE', 'INSERT', 'DELETE', 'EXTRACT',
            'SURROUND', 'FLOODFILL', 'INVERT', 'COLOR'
        ]
        
        # Vérifier qu'au moins une commande valide est présente
        template_upper = template.upper()
        has_valid_command = any(cmd in template_upper for cmd in valid_commands)
        
        # Vérifier l'équilibrage des accolades et parenthèses
        brace_count = template.count('{') - template.count('}')
        paren_count = template.count('(') - template.count(')')
        bracket_count = template.count('[') - template.count(']')
        
        return (has_valid_command and 
                brace_count == 0 and 
                paren_count == 0 and 
                bracket_count == 0)
    
    def export_templates(self, filepath: str):
        """Exporte les templates vers un fichier JSON."""
        export_data = {}
        
        for category, scenario in self.category_templates.items():
            export_data[category] = {
                'template': scenario.template,
                'constraints': [
                    {
                        'param_name': c.param_name,
                        'param_type': c.param_type.value,
                        'constraint_type': c.constraint_type,
                        'constraint_data': c.constraint_data
                    }
                    for c in scenario.constraints
                ],
                'category': scenario.category,
                'confidence': scenario.confidence
            }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    def import_templates(self, filepath: str):
        """Importe des templates depuis un fichier JSON."""
        with open(filepath, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        for category, data in import_data.items():
            constraints = [
                ParameterConstraint(
                    param_name=c['param_name'],
                    param_type=ParameterType(c['param_type']),
                    constraint_type=c['constraint_type'],
                    constraint_data=c.get('constraint_data')
                )
                for c in data['constraints']
            ]
            
            self.category_templates[category] = GeneralizedScenario(
                template=data['template'],
                constraints=constraints,
                category=data['category'],
                confidence=data.get('confidence', 1.0)
            )